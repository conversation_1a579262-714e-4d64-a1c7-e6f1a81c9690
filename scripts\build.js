const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建桌面AI助手...');

// 检查必要文件
const checkFiles = () => {
  console.log('📋 检查必要文件...');
  
  const requiredFiles = [
    'docs/env',
    'assets/icons',
    'src/main/main.ts',
    'src/renderer/index.tsx'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      console.error(`❌ 缺少必要文件: ${file}`);
      process.exit(1);
    }
  }
  
  console.log('✅ 文件检查通过');
};

// 清理旧的构建文件
const cleanup = () => {
  console.log('🧹 清理旧的构建文件...');
  
  const dirsToClean = ['dist', 'release'];
  
  for (const dir of dirsToClean) {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`🗑️  删除 ${dir} 目录`);
    }
  }
};

// 安装依赖
const installDependencies = () => {
  console.log('📦 安装依赖...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ 依赖安装完成');
  } catch (error) {
    console.error('❌ 依赖安装失败:', error.message);
    process.exit(1);
  }
};

// 创建图标
const createIcons = () => {
  console.log('🎨 创建图标...');
  try {
    execSync('npm run create-icons', { stdio: 'inherit' });
    console.log('✅ 图标创建完成');
  } catch (error) {
    console.error('❌ 图标创建失败:', error.message);
    process.exit(1);
  }
};

// 构建应用
const buildApp = () => {
  console.log('🔨 构建应用...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ 应用构建完成');
  } catch (error) {
    console.error('❌ 应用构建失败:', error.message);
    process.exit(1);
  }
};

// 打包应用
const packageApp = () => {
  console.log('📦 打包应用...');
  try {
    execSync('npm run dist:win', { stdio: 'inherit' });
    console.log('✅ 应用打包完成');
  } catch (error) {
    console.error('❌ 应用打包失败:', error.message);
    process.exit(1);
  }
};

// 显示构建结果
const showResults = () => {
  console.log('\n🎉 构建完成！');
  console.log('\n📁 输出文件位置:');
  
  const releaseDir = path.join(__dirname, '../release');
  if (fs.existsSync(releaseDir)) {
    const files = fs.readdirSync(releaseDir);
    files.forEach(file => {
      const filePath = path.join(releaseDir, file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024 / 1024).toFixed(2);
      console.log(`  📄 ${file} (${size} MB)`);
    });
  }
  
  console.log('\n🚀 可以开始安装和使用了！');
};

// 主构建流程
const main = () => {
  try {
    checkFiles();
    cleanup();
    installDependencies();
    createIcons();
    buildApp();
    packageApp();
    showResults();
  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkFiles,
  cleanup,
  installDependencies,
  createIcons,
  buildApp,
  packageApp,
  showResults,
  main
};
