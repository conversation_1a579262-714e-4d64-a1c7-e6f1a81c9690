@echo off
echo 🚀 以管理员权限构建桌面AI助手
echo.

echo 📋 检查管理员权限...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 已获得管理员权限
) else (
    echo ❌ 需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 🧹 清理旧文件...
if exist release rmdir /s /q release
if exist dist rmdir /s /q dist

echo 🔧 重新构建...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo 📦 打包应用...
call npm run pack
if %errorlevel% neq 0 (
    echo ❌ 打包失败，尝试无签名打包...
    call npm run pack-no-sign
)

echo ✅ 构建完成！
echo 📁 exe文件位置: release\win-unpacked\桌面AI助手.exe
echo.
pause
