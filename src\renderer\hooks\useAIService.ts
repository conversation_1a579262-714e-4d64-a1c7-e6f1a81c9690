import { useCallback, useEffect, useRef } from 'react';
import { App } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import { useAppStore } from '../store/useAppStore';
import { Message } from '@shared/types';

export const useAIService = () => {
  const { config, setLoading, addMessage, updateMessage, messages } = useAppStore();
  const currentStreamingSessionRef = useRef<string | null>(null);
  const streamingMessageRef = useRef<string>('');
  const currentAssistantMessageIdRef = useRef<string | null>(null);
  const { message } = App.useApp();

  // 添加助手消息的内部方法
  const addAssistantMessage = useCallback(async (content: string, sessionId: string): Promise<string> => {
    const assistantMessage: Message = {
      id: uuidv4(),
      role: 'assistant',
      content,
      timestamp: Date.now(),
      sessionId
    };

    addMessage(assistantMessage);
    return assistantMessage.id;
  }, [addMessage]);

  // 更新助手消息的内部方法
  const updateLastAssistantMessage = useCallback((sessionId: string, content: string) => {
    if (currentAssistantMessageIdRef.current) {
      updateMessage(sessionId, currentAssistantMessageIdRef.current, {
        content,
        timestamp: Date.now()
      });
    }
  }, [updateMessage]);

  // 初始化AI服务监听器
  const initializeAI = useCallback(() => {
    // 监听流式响应块
    window.electronAPI.ai.onStreamChunk((data: { sessionId: string; chunk: string; fullMessage: string }) => {
      const { sessionId, chunk, fullMessage } = data;

      if (currentStreamingSessionRef.current === sessionId) {
        streamingMessageRef.current = fullMessage;
        updateLastAssistantMessage(sessionId, fullMessage);
      }
    });

    // 监听流式响应结束
    window.electronAPI.ai.onStreamEnd((data: { sessionId: string; fullMessage: string }) => {
      const { sessionId, fullMessage } = data;

      if (currentStreamingSessionRef.current === sessionId) {
        // 确保最终消息是完整的
        updateLastAssistantMessage(sessionId, fullMessage);

        // 清理状态
        currentStreamingSessionRef.current = null;
        streamingMessageRef.current = '';
        currentAssistantMessageIdRef.current = null;
        setLoading(false);
      }
    });

    // 监听错误
    window.electronAPI.ai.onError((error: string) => {
      console.error('AI服务错误:', error);
      message.error(`AI服务错误: ${error}`);

      // 清理状态
      currentStreamingSessionRef.current = null;
      streamingMessageRef.current = '';
      currentAssistantMessageIdRef.current = null;
      setLoading(false);
    });

    console.log('AI服务监听器已初始化');
  }, [updateLastAssistantMessage, setLoading]);

  // 发送消息到AI
  const sendToAI = useCallback(async (userMessage: string, sessionId: string) => {
    if (!config?.apiKey) {
      message.error('请先配置API密钥');
      return;
    }

    try {
      setLoading(true);
      currentStreamingSessionRef.current = sessionId;
      streamingMessageRef.current = '';

      // 确保AI服务监听器已初始化
      initializeAI();

      // 创建一个空的助手消息，用于显示流式响应
      const assistantMessageId = await addAssistantMessage('', sessionId);
      currentAssistantMessageIdRef.current = assistantMessageId;

      // 发送消息到主进程
      await window.electronAPI.ai.sendMessage(userMessage, sessionId);

    } catch (error) {
      console.error('发送AI消息失败:', error);
      message.error('发送消息失败，请重试');

      // 清理状态
      currentStreamingSessionRef.current = null;
      streamingMessageRef.current = '';
      currentAssistantMessageIdRef.current = null;
      setLoading(false);
    }
  }, [config, setLoading, addAssistantMessage, initializeAI]);

  // 停止当前的AI响应
  const stopAIResponse = useCallback(() => {
    if (currentStreamingSessionRef.current) {
      // 这里可以添加停止AI响应的逻辑
      currentStreamingSessionRef.current = null;
      streamingMessageRef.current = '';
      currentAssistantMessageIdRef.current = null;
      setLoading(false);

      message.info('已停止AI响应');
    }
  }, [setLoading]);

  // 检查AI服务状态
  const checkAIStatus = useCallback(async () => {
    if (!config?.apiKey) {
      return { status: 'error', message: 'API密钥未配置' };
    }

    try {
      // 这里可以添加ping AI服务的逻辑
      return { status: 'ok', message: 'AI服务正常' };
    } catch (error) {
      return { status: 'error', message: 'AI服务连接失败' };
    }
  }, [config]);

  // 清理监听器
  const cleanup = useCallback(() => {
    window.electronAPI.ai.removeAllListeners();
    currentStreamingSessionRef.current = null;
    streamingMessageRef.current = '';
    currentAssistantMessageIdRef.current = null;
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    initializeAI,
    sendToAI,
    stopAIResponse,
    checkAIStatus,
    cleanup,
    isStreaming: currentStreamingSessionRef.current !== null
  };
};
