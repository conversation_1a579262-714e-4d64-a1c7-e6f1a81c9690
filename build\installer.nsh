; 自定义NSIS安装脚本

; 安装前检查
!macro preInit
  ; 检查是否已经有实例在运行
  System::Call 'kernel32::CreateMutex(i 0, i 0, t "DesktopAIAssistantMutex") i .r1 ?e'
  Pop $R0
  StrCmp $R0 0 +3
    MessageBox MB_OK|MB_ICONEXCLAMATION "桌面AI助手已经在运行中，请先关闭后再安装。"
    Abort
!macroend

; 安装完成后
!macro customInstall
  ; 创建卸载信息
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "DisplayName" "桌面AI助手"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "DisplayVersion" "${VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "Publisher" "Desktop AI Assistant"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "DisplayIcon" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "UninstallString" "$INSTDIR\Uninstall ${PRODUCT_FILENAME}.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "InstallLocation" "$INSTDIR"
  
  ; 估算安装大小
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}" "EstimatedSize" "$0"
!macroend

; 卸载前
!macro customUnInstall
  ; 停止应用进程
  nsExec::ExecToLog 'taskkill /F /IM "${APP_EXECUTABLE_FILENAME}" /T'
  
  ; 删除注册表项
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${UNINSTALL_APP_KEY}"
  
  ; 询问是否删除用户数据
  MessageBox MB_YESNO|MB_ICONQUESTION "是否删除所有用户数据（包括聊天记录和设置）？" IDNO +3
    RMDir /r "$APPDATA\${PRODUCT_FILENAME}"
    RMDir /r "$LOCALAPPDATA\${PRODUCT_FILENAME}"
!macroend

; 安装页面自定义
!macro customWelcomePage
  !insertmacro MUI_PAGE_WELCOME
!macroend

; 许可协议页面
!macro customLicensePage
  !insertmacro MUI_PAGE_LICENSE "build\license.txt"
!macroend

; 安装目录选择页面
!macro customDirectoryPage
  !insertmacro MUI_PAGE_DIRECTORY
!macroend

; 开始菜单页面
!macro customStartMenuPage
  !insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder
!macroend

; 安装进度页面
!macro customInstallPage
  !insertmacro MUI_PAGE_INSTFILES
!macroend

; 完成页面
!macro customFinishPage
  !define MUI_FINISHPAGE_RUN "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
  !define MUI_FINISHPAGE_RUN_TEXT "立即启动桌面AI助手"
  !define MUI_FINISHPAGE_LINK "访问项目主页"
  !define MUI_FINISHPAGE_LINK_LOCATION "https://github.com/desktop-ai-assistant"
  !insertmacro MUI_PAGE_FINISH
!macroend
