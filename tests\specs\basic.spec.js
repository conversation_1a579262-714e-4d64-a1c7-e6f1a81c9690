/**
 * 基础功能自动化测试
 * 测试应用启动、界面加载、新建会话等基础功能
 */

const { test, expect } = require('@playwright/test');
const { _electron: electron } = require('playwright');
const path = require('path');
const { createTestHelpers } = require('../utils/helpers');
const { SELECTORS } = require('../utils/selectors');

test.describe('基础功能测试', () => {
  let electronApp;
  let page;
  let helpers;

  test.beforeAll(async () => {
    // 启动Electron应用
    electronApp = await electron.launch({
      args: [path.join(__dirname, '../../dist/main/main.js')],
      env: {
        NODE_ENV: 'test',
        ELECTRON_IS_DEV: '1'
      }
    });

    // 获取第一个页面
    page = await electronApp.firstWindow();
    
    // 创建测试辅助工具
    helpers = createTestHelpers(page);
    
    // 开始控制台日志监听
    helpers.startConsoleLogging();
    
    // 等待应用加载完成
    await page.waitForLoadState('domcontentloaded');
    await helpers.waitForLoad();
  });

  test.afterAll(async () => {
    // 清理并关闭应用
    if (helpers) {
      await helpers.cleanup();
    }
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('应用启动测试', async () => {
    // 验证应用容器存在
    await helpers.assertElementExists(SELECTORS.app.container, '应用容器应该存在');
    
    // 验证窗口标题
    const title = await page.title();
    expect(title).toContain('桌面AI助手');
    
    // 截图记录
    await helpers.takeScreenshot('app-startup');
    
    console.log('✅ 应用启动测试通过');
  });

  test('界面元素加载测试', async () => {
    // 验证侧边栏存在
    await helpers.assertElementExists(SELECTORS.sidebar.container, '侧边栏应该存在');
    
    // 验证聊天容器存在
    await helpers.assertElementExists(SELECTORS.chat.container, '聊天容器应该存在');
    
    // 验证新建会话按钮存在
    await helpers.assertElementExists(SELECTORS.sidebar.newSessionButton, '新建会话按钮应该存在');
    
    // 验证设置按钮存在
    await helpers.assertElementExists(SELECTORS.sidebar.settingsButton, '设置按钮应该存在');
    
    console.log('✅ 界面元素加载测试通过');
  });

  test('新建会话功能测试', async () => {
    // 点击新建会话按钮
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    
    // 等待会话创建完成
    await page.waitForTimeout(1000);
    
    // 验证会话列表中有新会话
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    const sessionCount = await sessionItems.count();
    expect(sessionCount).toBeGreaterThan(0);
    
    // 验证聊天输入区域可见
    await helpers.assertElementExists(SELECTORS.chat.inputArea, '聊天输入区域应该可见');
    
    // 验证输入框可用
    const inputField = page.locator(SELECTORS.chat.inputField);
    await expect(inputField).toBeEnabled();
    
    // 截图记录
    await helpers.takeScreenshot('new-session-created');
    
    console.log('✅ 新建会话功能测试通过');
  });

  test('会话切换功能测试', async () => {
    // 创建第二个会话
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
    
    // 获取所有会话项
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    const sessionCount = await sessionItems.count();
    expect(sessionCount).toBeGreaterThanOrEqual(2);
    
    // 点击第一个会话
    await sessionItems.first().click();
    await page.waitForTimeout(500);
    
    // 验证会话已切换（检查active状态）
    const firstSession = sessionItems.first();
    await expect(firstSession).toHaveClass(/active/);
    
    // 点击第二个会话
    if (sessionCount > 1) {
      await sessionItems.nth(1).click();
      await page.waitForTimeout(500);
      
      // 验证第二个会话变为active
      const secondSession = sessionItems.nth(1);
      await expect(secondSession).toHaveClass(/active/);
    }
    
    console.log('✅ 会话切换功能测试通过');
  });

  test('设置界面打开测试', async () => {
    // 点击设置按钮
    await helpers.safeClick(SELECTORS.sidebar.settingsButton);
    
    // 等待设置模态框出现
    await helpers.waitForElement(SELECTORS.settings.modal);
    
    // 验证设置模态框可见
    await helpers.assertElementExists(SELECTORS.settings.modal, '设置模态框应该可见');
    
    // 验证API密钥输入框存在
    await helpers.assertElementExists(SELECTORS.settings.apiKeyInput, 'API密钥输入框应该存在');
    
    // 验证保存按钮存在
    await helpers.assertElementExists(SELECTORS.settings.saveButton, '保存按钮应该存在');
    
    // 验证取消按钮存在
    await helpers.assertElementExists(SELECTORS.settings.cancelButton, '取消按钮应该存在');
    
    // 截图记录
    await helpers.takeScreenshot('settings-modal-open');
    
    // 关闭设置模态框
    await helpers.safeClick(SELECTORS.settings.cancelButton);
    
    // 等待模态框关闭
    await page.waitForTimeout(500);
    
    console.log('✅ 设置界面打开测试通过');
  });

  test('应用响应性测试', async () => {
    // 测试窗口大小调整
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(500);
    
    // 验证界面仍然正常显示
    await helpers.assertElementExists(SELECTORS.app.container, '调整窗口大小后应用容器应该仍然存在');
    await helpers.assertElementExists(SELECTORS.sidebar.container, '调整窗口大小后侧边栏应该仍然存在');
    
    // 测试较小窗口
    await page.setViewportSize({ width: 800, height: 600 });
    await page.waitForTimeout(500);
    
    // 验证界面适应性
    await helpers.assertElementExists(SELECTORS.app.container, '小窗口下应用容器应该仍然存在');
    
    console.log('✅ 应用响应性测试通过');
  });

  test('控制台错误检查', async () => {
    // 获取控制台日志
    const logs = helpers.getConsoleLogs();
    
    // 检查是否有严重错误
    const errors = logs.filter(log => log.type === 'error');
    const criticalErrors = errors.filter(error => 
      !error.text.includes('DevTools') && 
      !error.text.includes('Extension') &&
      !error.text.includes('favicon')
    );
    
    // 如果有关键错误，输出详情
    if (criticalErrors.length > 0) {
      console.log('⚠️ 发现控制台错误:');
      criticalErrors.forEach(error => {
        console.log(`  - ${error.text}`);
      });
    }
    
    // 验证没有关键错误（允许一些非关键错误）
    expect(criticalErrors.length).toBeLessThanOrEqual(2);
    
    console.log('✅ 控制台错误检查通过');
  });

  test('内存泄漏基础检查', async () => {
    // 执行一些操作来检查内存使用
    for (let i = 0; i < 3; i++) {
      await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
      await page.waitForTimeout(500);
    }
    
    // 检查页面是否仍然响应
    await helpers.assertElementExists(SELECTORS.app.container, '多次操作后应用应该仍然响应');
    
    // 简单的内存使用检查（通过页面响应性）
    const startTime = Date.now();
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    const endTime = Date.now();
    
    // 验证操作响应时间合理（小于3秒）
    const responseTime = endTime - startTime;
    expect(responseTime).toBeLessThan(3000);
    
    console.log(`✅ 内存泄漏基础检查通过 (响应时间: ${responseTime}ms)`);
  });
});
