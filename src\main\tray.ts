import { Tray, Menu, nativeImage, app } from 'electron';
import * as path from 'path';
import { APP_CONFIG } from '@shared/constants';

export interface TrayCallbacks {
  onShowWindow: () => void;
  onNewSession: () => void;
  onOpenSettings: () => void;
  onShowAbout: () => void;
}

export class TrayManager {
  private tray: Tray | null = null;
  private callbacks: TrayCallbacks | null = null;

  createTray(onTrayClick: () => void, callbacks?: TrayCallbacks): void {
    this.callbacks = callbacks || null;

    try {
      console.log('🔍 开始创建托盘...');

      // 直接使用简单图标，避免文件路径问题
      const trayIcon = this.createSimpleIcon();
      console.log('🔍 简单图标创建完成，是否为空:', trayIcon.isEmpty());

      if (trayIcon.isEmpty()) {
        console.error('❌ 简单图标也为空');
        return;
      }

      this.tray = new Tray(trayIcon);
      console.log('✅ 托盘创建成功');
    } catch (error) {
      console.error('❌ 托盘创建失败:', error);
      return;
    }
    
    // 设置托盘提示文本
    this.tray.setToolTip(APP_CONFIG.APP_NAME);
    
    // 设置托盘菜单
    this.updateTrayMenu();
    
    // 托盘图标点击事件
    this.tray.on('click', () => {
      onTrayClick();
    });
    
    // 右键点击显示菜单
    this.tray.on('right-click', () => {
      if (this.tray) {
        this.tray.popUpContextMenu();
      }
    });
  }

  updateTrayMenu(): void {
    if (!this.tray) return;

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示窗口',
        click: () => {
          if (this.callbacks?.onShowWindow) {
            this.callbacks.onShowWindow();
          }
        }
      },
      {
        type: 'separator'
      },
      {
        label: '新建会话',
        click: () => {
          if (this.callbacks?.onNewSession) {
            this.callbacks.onNewSession();
          }
        }
      },
      {
        label: '设置',
        click: () => {
          if (this.callbacks?.onOpenSettings) {
            this.callbacks.onOpenSettings();
          }
        }
      },
      {
        type: 'separator'
      },
      {
        label: '关于',
        click: () => {
          if (this.callbacks?.onShowAbout) {
            this.callbacks.onShowAbout();
          }
        }
      },
      {
        label: '退出',
        click: () => {
          (app as any).isQuiting = true;
          app.quit();
        }
      }
    ]);

    this.tray.setContextMenu(contextMenu);
  }

  setTrayIcon(iconType: 'normal' | 'thinking' | 'error' = 'normal'): void {
    if (!this.tray) return;

    const iconPath = this.getTrayIconPath(iconType);
    const trayIcon = nativeImage.createFromPath(iconPath);
    trayIcon.setTemplateImage(true);
    
    this.tray.setImage(trayIcon);
  }

  setTrayTooltip(tooltip: string): void {
    if (this.tray) {
      this.tray.setToolTip(tooltip);
    }
  }

  destroy(): void {
    if (this.tray) {
      this.tray.destroy();
      this.tray = null;
    }
  }

  private getTrayIconPath(iconType: 'normal' | 'thinking' | 'error' = 'normal'): string {
    // 根据操作系统和图标类型返回相应的图标路径
    const platform = process.platform;
    let iconName = 'tray';

    switch (iconType) {
      case 'thinking':
        iconName = 'tray-thinking';
        break;
      case 'error':
        iconName = 'tray-error';
        break;
      default:
        iconName = 'tray';
    }

    // 在打包后的应用中，图标文件需要从正确的路径加载
    let iconPath: string;

    if (app.isPackaged) {
      // 打包后的应用，图标在resources目录外
      iconPath = path.join(process.resourcesPath, 'assets', 'icons');
    } else {
      // 开发环境
      iconPath = path.join(__dirname, '../../assets/icons');
    }

    if (platform === 'win32') {
      // 先尝试ICO文件，如果不存在则使用PNG
      const icoPath = path.join(iconPath, `${iconName}.ico`);
      const pngPath = path.join(iconPath, `${iconName}.png`);

      if (require('fs').existsSync(icoPath)) {
        return icoPath;
      } else if (require('fs').existsSync(pngPath)) {
        return pngPath;
      } else {
        // 如果都不存在，返回ICO路径（会在调试信息中显示）
        return icoPath;
      }
    } else if (platform === 'darwin') {
      return path.join(iconPath, `${iconName}Template.png`);
    } else {
      return path.join(iconPath, `${iconName}.png`);
    }
  }

  private createSimpleIcon(): Electron.NativeImage {
    // 创建一个简单的1x1像素的透明图标作为备选
    // 这是最简单的方法，确保托盘能创建成功
    const buffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x10,
      0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0xF3, 0xFF, 0x61, 0x00, 0x00, 0x00,
      0x0D, 0x49, 0x44, 0x41, 0x54, 0x38, 0x8D, 0x63, 0x60, 0x18, 0x05, 0xA3,
      0x60, 0x14, 0x8C, 0x02, 0x08, 0x00, 0x00, 0x04, 0x10, 0x00, 0x01, 0x27,
      0x6B, 0xB7, 0x4D, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE,
      0x42, 0x60, 0x82
    ]);

    return nativeImage.createFromBuffer(buffer);
  }
}
