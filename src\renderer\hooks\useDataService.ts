import { useCallback } from 'react';
import { useAppStore } from '../store/useAppStore';
import { AppConfig, Session, Message } from '@shared/types';
import { STORAGE_KEYS } from '@shared/constants';

export const useDataService = () => {
  const {
    setConfig,
    setSessions,
    setMessages,
    setCurrentSession,
    sessions,
    messages
  } = useAppStore();

  // 加载初始数据
  const loadInitialData = useCallback(async () => {
    try {
      // 加载应用配置
      const config = await window.electronAPI.store.get(STORAGE_KEYS.APP_CONFIG);
      if (config) {
        setConfig(config);
      }

      // 加载会话列表
      const sessionsData = await window.electronAPI.store.get(STORAGE_KEYS.SESSIONS);
      if (sessionsData) {
        setSessions(sessionsData);
      }

      // 加载消息数据
      const messagesData = await window.electronAPI.store.get(STORAGE_KEYS.MESSAGES);
      if (messagesData) {
        // 为每个会话设置消息
        Object.keys(messagesData).forEach(sessionId => {
          setMessages(sessionId, messagesData[sessionId]);
        });
      }

      // 加载当前会话
      const currentSessionId = await window.electronAPI.store.get(STORAGE_KEYS.CURRENT_SESSION);
      if (currentSessionId && sessionsData && sessionsData[currentSessionId]) {
        setCurrentSession(currentSessionId);
      }

      console.log('初始数据加载完成');
    } catch (error) {
      console.error('加载初始数据失败:', error);
    }
  }, [setConfig, setSessions, setMessages, setCurrentSession]);

  // 保存应用配置
  const saveConfig = useCallback(async (config: AppConfig) => {
    try {
      await window.electronAPI.store.set(STORAGE_KEYS.APP_CONFIG, config);
      setConfig(config);
    } catch (error) {
      console.error('保存配置失败:', error);
      throw error;
    }
  }, [setConfig]);

  // 保存会话数据
  const saveSessions = useCallback(async (sessionsData: { [key: string]: Session }) => {
    try {
      await window.electronAPI.store.set(STORAGE_KEYS.SESSIONS, sessionsData);
      setSessions(sessionsData);
    } catch (error) {
      console.error('保存会话数据失败:', error);
      throw error;
    }
  }, [setSessions]);

  // 保存消息数据
  const saveMessages = useCallback(async (sessionId: string, messagesData: Message[]) => {
    try {
      // 获取当前所有消息数据
      const allMessages = await window.electronAPI.store.get(STORAGE_KEYS.MESSAGES) || {};
      
      // 更新指定会话的消息
      allMessages[sessionId] = messagesData;
      
      // 保存到存储
      await window.electronAPI.store.set(STORAGE_KEYS.MESSAGES, allMessages);
      
      // 更新状态
      setMessages(sessionId, messagesData);
    } catch (error) {
      console.error('保存消息数据失败:', error);
      throw error;
    }
  }, [setMessages]);

  // 保存当前会话ID
  const saveCurrentSession = useCallback(async (sessionId: string | null) => {
    try {
      if (sessionId) {
        await window.electronAPI.store.set(STORAGE_KEYS.CURRENT_SESSION, sessionId);
      } else {
        await window.electronAPI.store.delete(STORAGE_KEYS.CURRENT_SESSION);
      }
      setCurrentSession(sessionId);
    } catch (error) {
      console.error('保存当前会话失败:', error);
      throw error;
    }
  }, [setCurrentSession]);

  // 删除会话数据
  const deleteSessionData = useCallback(async (sessionId: string) => {
    try {
      // 删除会话
      const currentSessions = { ...sessions };
      delete currentSessions[sessionId];
      await saveSessions(currentSessions);

      // 删除消息
      const allMessages = await window.electronAPI.store.get(STORAGE_KEYS.MESSAGES) || {};
      delete allMessages[sessionId];
      await window.electronAPI.store.set(STORAGE_KEYS.MESSAGES, allMessages);

      // 如果删除的是当前会话，清除当前会话
      const currentSessionId = await window.electronAPI.store.get(STORAGE_KEYS.CURRENT_SESSION);
      if (currentSessionId === sessionId) {
        await saveCurrentSession(null);
      }
    } catch (error) {
      console.error('删除会话数据失败:', error);
      throw error;
    }
  }, [sessions, saveSessions, saveCurrentSession]);

  // 导出数据
  const exportData = useCallback(async () => {
    try {
      const sessionsData = await window.electronAPI.store.get(STORAGE_KEYS.SESSIONS);
      const messagesData = await window.electronAPI.store.get(STORAGE_KEYS.MESSAGES);
      
      return {
        sessions: sessionsData || {},
        messages: messagesData || {},
        exportTime: new Date().toISOString()
      };
    } catch (error) {
      console.error('导出数据失败:', error);
      throw error;
    }
  }, []);

  // 导入数据
  const importData = useCallback(async (data: any) => {
    try {
      if (data.sessions) {
        await saveSessions(data.sessions);
      }
      
      if (data.messages) {
        await window.electronAPI.store.set(STORAGE_KEYS.MESSAGES, data.messages);
        
        // 更新状态中的所有消息
        Object.keys(data.messages).forEach(sessionId => {
          setMessages(sessionId, data.messages[sessionId]);
        });
      }
      
      console.log('数据导入完成');
    } catch (error) {
      console.error('导入数据失败:', error);
      throw error;
    }
  }, [saveSessions, setMessages]);

  return {
    loadInitialData,
    saveConfig,
    saveSessions,
    saveMessages,
    saveCurrentSession,
    deleteSessionData,
    exportData,
    importData
  };
};
