# 🚀 桌面AI助手 - 完整启动指南

## 📋 系统要求
- Windows 10/11
- Node.js 18+
- 8GB+ RAM
- 网络连接（用于AI API调用）

## ⚡ 快速启动

### 方法一：一键启动（推荐）
```bash
node start-dev-environment.js
```

### 方法二：分步启动
```bash
# 1. 启动开发环境
npm run dev

# 2. 启动Electron应用
npm start
```

## 🔧 详细启动步骤

### 1. 环境检查
确保以下文件存在：
- ✅ `node_modules/` - 依赖已安装
- ✅ `docs/env` - API密钥配置
- ✅ `dist/` - 编译输出目录

### 2. 编译主进程
```bash
npx webpack --config webpack.main.config.js --mode development
```
预期输出：
```
✅ webpack 5.100.2 compiled successfully in 7949 ms
```

### 3. 启动前端开发服务器
```bash
npx webpack serve --config webpack.renderer.config.js
```
预期结果：
- 🌐 前端服务器: http://localhost:3000
- 🔥 热重载已启用

### 4. 启动Electron应用
```bash
npm start
```
预期结果：
- ⚡ Electron桌面应用启动
- 🪟 应用窗口正常显示

## 🧪 功能测试

### 测试API连接
```bash
node test-real-api.js
```
预期输出：
```
✅ 非流式API: 通过
✅ 流式API: 通过
🎉 所有API测试通过！
```

### 测试前端界面
1. 浏览器访问: http://localhost:3000
2. 点击"新建会话"
3. 输入测试消息
4. 验证界面响应

### 测试完整功能
1. 启动Electron应用
2. 创建新会话
3. 发送消息给AI
4. 验证流式响应
5. 检查消息历史

## 📊 服务状态检查

### 端口占用检查
- **3000**: 前端开发服务器
- **Electron**: 桌面应用进程

### 进程监控
```bash
# 查看Node.js进程
tasklist | findstr node

# 查看Electron进程
tasklist | findstr electron
```

## 🛠️ 常见问题解决

### 问题1: 端口被占用
```bash
# 查找占用端口3000的进程
netstat -ano | findstr :3000

# 终止进程（替换PID）
taskkill /PID <PID> /F
```

### 问题2: API调用失败
检查：
- 网络连接
- API密钥有效性
- 防火墙设置

### 问题3: Electron启动失败
解决方案：
```bash
# 重新安装Electron
npm install electron --save-dev

# 清理缓存
npm run clean
npm install
```

### 问题4: 前端编译错误
解决方案：
```bash
# 清理node_modules
rm -rf node_modules package-lock.json
npm install

# 重新编译
npm run build:renderer
```

## 🔍 调试模式

### 启用开发者工具
- **前端**: 浏览器F12
- **Electron**: Ctrl+Shift+I

### 查看日志
- **前端日志**: 浏览器控制台
- **主进程日志**: 终端输出
- **API日志**: test-real-api.js输出

## 📱 使用说明

### 基本操作
1. **新建会话**: 点击侧边栏"新建会话"按钮
2. **发送消息**: 在输入框输入内容，点击发送或按Enter
3. **查看历史**: 点击侧边栏的会话项
4. **设置配置**: 点击设置按钮

### 快捷键
- `Enter`: 发送消息
- `Shift + Enter`: 换行
- `Ctrl + N`: 新建会话
- `Ctrl + ,`: 打开设置

## 🎯 开发建议

### 代码修改
- **前端代码**: 自动热重载，无需重启
- **主进程代码**: 需要重启Electron应用
- **配置文件**: 需要重新编译

### 性能优化
- 使用生产模式构建: `npm run build`
- 启用代码分割
- 优化图片资源

## 📋 部署准备

### 构建生产版本
```bash
npm run build
```

### 打包应用
```bash
npm run dist
```

### 生成安装包
```bash
npm run dist:win
```

## 🎉 成功标志

当看到以下输出时，表示环境启动成功：

```
🎉 开发环境启动完成！

📋 服务状态:
  • 前端开发服务器: http://localhost:3000
  • Electron应用: 已启动
  • API服务: 通义千问API已配置

💡 使用说明:
  • 在Electron应用中测试完整功能
  • 在浏览器中访问 http://localhost:3000 进行UI调试
  • 修改代码后前端会自动热重载
  • 修改主进程代码需要重启Electron
```

## 📞 技术支持

如遇到问题，请检查：
1. 📋 开发环境测试报告.md
2. 🧪 test-real-api.js 输出
3. 📝 控制台错误信息
4. 🔧 网络连接状态

---

**祝您开发愉快！** 🚀
