import { globalShortcut, BrowserWindow } from 'electron';

export class ShortcutManager {
  private mainWindow: BrowserWindow | null = null;

  constructor() {}

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  registerShortcuts(): void {
    try {
      // 注册全局快捷键：Ctrl+Shift+A 显示/隐藏窗口
      const toggleShortcut = globalShortcut.register('CommandOrControl+Shift+A', () => {
        this.toggleWindow();
      });

      if (!toggleShortcut) {
        console.warn('快捷键 CommandOrControl+Shift+A 注册失败');
      } else {
        console.log('快捷键 CommandOrControl+Shift+A 注册成功');
      }

      // 注册全局快捷键：Ctrl+Shift+N 新建会话
      const newSessionShortcut = globalShortcut.register('CommandOrControl+Shift+N', () => {
        this.createNewSession();
      });

      if (!newSessionShortcut) {
        console.warn('快捷键 CommandOrControl+Shift+N 注册失败');
      } else {
        console.log('快捷键 CommandOrControl+Shift+N 注册成功');
      }

    } catch (error) {
      console.error('注册快捷键失败:', error);
    }
  }

  unregisterShortcuts(): void {
    try {
      // 注销所有快捷键
      globalShortcut.unregisterAll();
      console.log('所有快捷键已注销');
    } catch (error) {
      console.error('注销快捷键失败:', error);
    }
  }

  private toggleWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isVisible()) {
        this.mainWindow.hide();
      } else {
        this.mainWindow.show();
        this.mainWindow.focus();
      }
    }
  }

  private createNewSession(): void {
    if (this.mainWindow) {
      // 显示窗口
      if (!this.mainWindow.isVisible()) {
        this.mainWindow.show();
        this.mainWindow.focus();
      }
      
      // 发送创建新会话的消息到渲染进程
      this.mainWindow.webContents.send('create-new-session');
    }
  }

  // 检查快捷键是否已注册
  isRegistered(accelerator: string): boolean {
    return globalShortcut.isRegistered(accelerator);
  }

  // 获取所有已注册的快捷键
  getRegisteredShortcuts(): string[] {
    // Electron没有直接的API获取所有已注册的快捷键
    // 这里返回我们知道的快捷键列表
    const shortcuts = [];
    
    if (this.isRegistered('CommandOrControl+Shift+A')) {
      shortcuts.push('CommandOrControl+Shift+A');
    }
    
    if (this.isRegistered('CommandOrControl+Shift+N')) {
      shortcuts.push('CommandOrControl+Shift+N');
    }
    
    return shortcuts;
  }
}
