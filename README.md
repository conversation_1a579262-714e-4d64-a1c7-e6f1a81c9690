# 桌面AI助手

一个基于Electron + React + TypeScript的Windows桌面AI助手应用程序，集成通义千问API，提供智能对话功能。

## 功能特性

- 🤖 **AI对话**: 集成通义千问API，支持智能对话
- 💬 **会话管理**: 支持多会话管理，会话历史持久化
- 🎨 **现代化UI**: 基于Ant Design的现代化用户界面
- 📌 **系统托盘**: 常驻系统托盘，随时可用
- 🚀 **自动启动**: 支持开机自动启动
- 💾 **本地存储**: 聊天历史本地安全存储
- 🔄 **流式响应**: 支持AI流式响应，实时显示

## 技术栈

- **前端框架**: React 18 + TypeScript
- **桌面框架**: Electron 28
- **UI组件库**: Ant Design 5
- **状态管理**: Zustand
- **构建工具**: Webpack 5
- **AI API**: 通义千问 (OpenAI兼容接口)

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
# 安装依赖并创建图标
npm run setup

# 或者分步执行
npm install
npm run create-icons
```

### 开发模式

```bash
# 启动开发服务器
npm run dev

# 在另一个终端启动Electron
npm start
```

### 构建应用

```bash
# 构建应用
npm run build

# 打包为可执行文件
npm run pack

# 创建安装程序
npm run dist:win
```

## 配置说明

### API密钥配置

应用会自动从 `docs/env` 文件读取API密钥：

```
api-key=your-dashscope-api-key
```

### 应用配置

应用配置存储在用户数据目录中，包括：

- API设置（模型、温度、最大token等）
- 窗口位置和大小
- 自动启动设置

## 项目结构

```
desktop-ai-assistant/
├── src/
│   ├── main/                 # Electron主进程
│   │   ├── main.ts          # 主进程入口
│   │   ├── window.ts        # 窗口管理
│   │   ├── tray.ts          # 系统托盘
│   │   ├── store.ts         # 数据存储
│   │   ├── aiService.ts     # AI服务
│   │   ├── autoLaunch.ts    # 自动启动
│   │   └── preload.ts       # 预加载脚本
│   ├── renderer/            # React渲染进程
│   │   ├── components/      # React组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── store/          # 状态管理
│   │   ├── styles/         # 样式文件
│   │   ├── App.tsx         # 主应用组件
│   │   └── index.tsx       # 渲染进程入口
│   └── shared/             # 共享代码
│       ├── types.ts        # 类型定义
│       └── constants.ts    # 常量定义
├── assets/                 # 静态资源
├── docs/                   # 文档和配置
├── scripts/                # 构建脚本
└── public/                 # 公共文件
```

## 开发指南

### 添加新功能

1. 在 `src/shared/types.ts` 中定义相关类型
2. 在 `src/shared/constants.ts` 中添加常量
3. 在主进程中实现业务逻辑
4. 在渲染进程中实现UI界面
5. 通过IPC进行进程间通信

### 调试技巧

- 开发模式下会自动打开开发者工具
- 主进程日志在终端中查看
- 渲染进程日志在开发者工具中查看

## 部署说明

### Windows安装程序

```bash
npm run dist:win
```

生成的安装程序位于 `release/` 目录中。

### 系统要求

- Windows 10 或更高版本
- .NET Framework 4.5 或更高版本

## 常见问题

### Q: 应用无法启动？
A: 检查Node.js版本是否符合要求，确保所有依赖已正确安装。

### Q: AI无法响应？
A: 检查API密钥是否正确配置，网络连接是否正常。

### Q: 托盘图标不显示？
A: 确保图标文件存在，运行 `npm run create-icons` 创建占位符图标。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
