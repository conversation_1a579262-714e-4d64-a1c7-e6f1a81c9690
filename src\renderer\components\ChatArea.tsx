import React, { useEffect, useRef } from 'react';
import { Empty, Spin } from 'antd';
import { MessageOutlined } from '@ant-design/icons';
import { useAppStore } from '../store/useAppStore';
import MessageList from './MessageList';
import MessageInput from './MessageInput';

const ChatArea: React.FC = () => {
  const { 
    currentSessionId, 
    sessions, 
    messages, 
    isLoading 
  } = useAppStore();

  const currentSession = currentSessionId ? sessions[currentSessionId] : null;
  const currentMessages = currentSessionId ? messages[currentSessionId] || [] : [];

  if (!currentSessionId || !currentSession) {
    return (
      <div
        style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column'
        }}
        data-testid="chat-empty-state"
      >
        <Empty
          image={<MessageOutlined style={{ fontSize: '64px', color: '#d9d9d9' }} />}
          description={
            <span style={{ color: '#999', fontSize: '16px' }}>
              选择一个会话开始聊天，或创建新会话
            </span>
          }
        />
      </div>
    );
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 会话标题 */}
      <div 
        style={{ 
          padding: '16px 24px',
          borderBottom: '1px solid #e8e8e8',
          background: '#fafafa'
        }}
      >
        <h3 style={{ margin: 0, fontSize: '16px', color: '#333' }}>
          {currentSession.title}
        </h3>
        <p style={{ margin: '4px 0 0 0', fontSize: '12px', color: '#666' }}>
          {currentMessages.length} 条消息
        </p>
      </div>

      {/* 消息列表 */}
      <div style={{ flex: 1, overflow: 'hidden' }} data-testid="message-list-container">
        {isLoading && currentMessages.length === 0 ? (
          <div
            style={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            data-testid="loading-spinner"
          >
            <Spin size="large" tip="加载中..." />
          </div>
        ) : (
          <MessageList
            messages={currentMessages}
            sessionId={currentSessionId}
          />
        )}
      </div>

      {/* 消息输入 */}
      <div
        style={{
          borderTop: '1px solid #e8e8e8',
          background: '#ffffff'
        }}
        data-testid="chat-input-area"
      >
        <MessageInput sessionId={currentSessionId} />
      </div>
    </div>
  );
};

export default ChatArea;
