import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useAppStore } from '../store/useAppStore';
import { useDataService } from './useDataService';
import { Message } from '@shared/types';

export const useMessageService = () => {
  const {
    messages,
    addMessage,
    updateMessage,
    currentSessionId
  } = useAppStore();
  
  const { saveMessages } = useDataService();

  // 发送用户消息
  const sendMessage = useCallback(async (content: string, sessionId: string) => {
    try {
      // 创建用户消息
      const userMessage: Message = {
        id: uuidv4(),
        role: 'user',
        content: content.trim(),
        timestamp: Date.now(),
        sessionId
      };

      // 添加到状态
      addMessage(userMessage);

      // 保存到存储
      const sessionMessages = messages[sessionId] || [];
      const updatedMessages = [...sessionMessages, userMessage];
      await saveMessages(sessionId, updatedMessages);

      return userMessage;

    } catch (error) {
      console.error('发送消息失败:', error);
      throw error;
    }
  }, [addMessage, messages, saveMessages]);

  // 添加助手消息
  const addAssistantMessage = useCallback(async (content: string, sessionId: string): Promise<string> => {
    const assistantMessage: Message = {
      id: uuidv4(),
      role: 'assistant',
      content,
      timestamp: Date.now(),
      sessionId
    };

    // 添加到状态
    addMessage(assistantMessage);

    // 保存到存储
    const sessionMessages = messages[sessionId] || [];
    const updatedMessages = [...sessionMessages, assistantMessage];
    await saveMessages(sessionId, updatedMessages);

    return assistantMessage.id;
  }, [addMessage, messages, saveMessages]);

  // 更新最后一条助手消息（用于流式响应）
  const updateLastAssistantMessage = useCallback(async (sessionId: string, content: string) => {
    const sessionMessages = messages[sessionId] || [];
    const lastMessage = sessionMessages[sessionMessages.length - 1];

    if (lastMessage && lastMessage.role === 'assistant') {
      // 更新状态
      updateMessage(sessionId, lastMessage.id, { 
        content,
        timestamp: Date.now()
      });

      // 保存到存储
      const updatedMessages = sessionMessages.map(msg =>
        msg.id === lastMessage.id 
          ? { ...msg, content, timestamp: Date.now() }
          : msg
      );
      await saveMessages(sessionId, updatedMessages);
    }
  }, [messages, updateMessage, saveMessages]);

  // 删除消息
  const deleteMessage = useCallback(async (sessionId: string, messageId: string) => {
    try {
      const sessionMessages = messages[sessionId] || [];
      const updatedMessages = sessionMessages.filter(msg => msg.id !== messageId);
      
      await saveMessages(sessionId, updatedMessages);
    } catch (error) {
      console.error('删除消息失败:', error);
      throw error;
    }
  }, [messages, saveMessages]);

  // 编辑消息
  const editMessage = useCallback(async (sessionId: string, messageId: string, newContent: string) => {
    try {
      // 更新状态
      updateMessage(sessionId, messageId, { 
        content: newContent.trim(),
        timestamp: Date.now()
      });

      // 保存到存储
      const sessionMessages = messages[sessionId] || [];
      const updatedMessages = sessionMessages.map(msg =>
        msg.id === messageId 
          ? { ...msg, content: newContent.trim(), timestamp: Date.now() }
          : msg
      );
      await saveMessages(sessionId, updatedMessages);
    } catch (error) {
      console.error('编辑消息失败:', error);
      throw error;
    }
  }, [updateMessage, messages, saveMessages]);

  // 重新生成助手回复
  const regenerateResponse = useCallback(async (sessionId: string, userMessageId: string) => {
    try {
      const sessionMessages = messages[sessionId] || [];
      const userMessageIndex = sessionMessages.findIndex(msg => msg.id === userMessageId);

      if (userMessageIndex === -1) {
        throw new Error('找不到用户消息');
      }

      const userMessage = sessionMessages[userMessageIndex];

      // 删除该用户消息之后的所有消息
      const messagesBeforeRegenerate = sessionMessages.slice(0, userMessageIndex + 1);
      await saveMessages(sessionId, messagesBeforeRegenerate);

      return userMessage;
    } catch (error) {
      console.error('重新生成回复失败:', error);
      throw error;
    }
  }, [messages, saveMessages]);

  // 获取会话的消息历史（用于AI上下文）
  const getConversationHistory = useCallback((sessionId: string, maxMessages: number = 20): Message[] => {
    const sessionMessages = messages[sessionId] || [];
    
    // 返回最近的消息，但保持对话的完整性
    if (sessionMessages.length <= maxMessages) {
      return sessionMessages;
    }

    // 从最后开始，确保包含完整的对话轮次
    const recentMessages = sessionMessages.slice(-maxMessages);
    
    // 如果第一条消息是助手消息，尝试包含前面的用户消息
    if (recentMessages[0]?.role === 'assistant') {
      const prevUserMessageIndex = sessionMessages.length - maxMessages - 1;
      if (prevUserMessageIndex >= 0 && sessionMessages[prevUserMessageIndex]?.role === 'user') {
        return sessionMessages.slice(prevUserMessageIndex);
      }
    }

    return recentMessages;
  }, [messages]);

  return {
    sendMessage,
    addAssistantMessage,
    updateLastAssistantMessage,
    deleteMessage,
    editMessage,
    regenerateResponse,
    getConversationHistory
  };
};
