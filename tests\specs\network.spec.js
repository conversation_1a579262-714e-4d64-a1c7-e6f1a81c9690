/**
 * 网络异常处理自动化测试
 * 测试网络异常情况下的fallback机制和错误处理
 */

const { test, expect } = require('@playwright/test');
const { _electron: electron } = require('playwright');
const path = require('path');
const { createTestHelpers } = require('../utils/helpers');
const { SELECTORS } = require('../utils/selectors');

test.describe('网络异常处理测试', () => {
  let electronApp;
  let page;
  let helpers;

  test.beforeAll(async () => {
    electronApp = await electron.launch({
      args: [path.join(__dirname, '../../dist/main/main.js')],
      env: {
        NODE_ENV: 'test',
        ELECTRON_IS_DEV: '1'
      }
    });

    page = await electronApp.firstWindow();
    helpers = createTestHelpers(page);
    helpers.startConsoleLogging();
    
    await page.waitForLoadState('domcontentloaded');
    await helpers.waitForLoad();
    
    // 创建一个新会话用于测试
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
  });

  test.afterAll(async () => {
    if (helpers) {
      await helpers.cleanup();
    }
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('网络连接失败处理测试', async () => {
    // 模拟网络错误
    await helpers.simulateNetworkError();
    
    const testMessage = '测试网络异常处理';
    
    // 发送消息
    await helpers.safeType(SELECTORS.chat.inputField, testMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待用户消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 等待一段时间让系统处理网络错误
    await page.waitForTimeout(5000);
    
    // 检查控制台日志中的网络错误处理
    const logs = helpers.getConsoleLogs();
    const networkErrorLogs = logs.filter(log => 
      log.text.includes('网络错误') || 
      log.text.includes('模拟模式') ||
      log.text.includes('切换到模拟模式') ||
      log.text.includes('API调用失败')
    );
    
    // 验证系统检测到了网络错误
    expect(networkErrorLogs.length).toBeGreaterThan(0);
    
    // 检查是否有AI响应（模拟响应）
    await page.waitForTimeout(3000);
    const aiMessages = page.locator(SELECTORS.chat.aiMessage);
    const aiMessageCount = await aiMessages.count();
    
    if (aiMessageCount > 0) {
      // 验证模拟响应的内容
      const aiMessage = aiMessages.last();
      const aiContent = await aiMessage.locator(SELECTORS.message.content).textContent();
      
      // 模拟响应应该包含相关提示
      expect(aiContent).toMatch(/(网络|连接|模拟|抱歉)/);
      
      console.log('✅ 网络连接失败处理测试通过 - 收到模拟响应');
    } else {
      console.log('✅ 网络连接失败处理测试通过 - 系统正确检测到网络错误');
    }
    
    // 恢复网络连接
    await helpers.restoreNetwork();
    
    // 截图记录
    await helpers.takeScreenshot('network-error-handling');
  });

  test('API超时处理测试', async () => {
    // 模拟API超时（通过拦截请求并延迟响应）
    await page.route('**/*dashscope.aliyuncs.com/**', async route => {
      // 延迟很长时间模拟超时
      await new Promise(resolve => setTimeout(resolve, 30000));
      route.continue();
    });
    
    const testMessage = '测试API超时处理';
    
    // 发送消息
    await helpers.safeType(SELECTORS.chat.inputField, testMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待用户消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 等待超时处理（应该比实际超时时间短）
    await page.waitForTimeout(10000);
    
    // 检查是否有超时处理的日志
    const logs = helpers.getConsoleLogs();
    const timeoutLogs = logs.filter(log => 
      log.text.includes('超时') || 
      log.text.includes('timeout') ||
      log.text.includes('模拟模式')
    );
    
    // 验证系统处理了超时情况
    expect(timeoutLogs.length).toBeGreaterThan(0);
    
    // 清除路由拦截
    await page.unroute('**/*dashscope.aliyuncs.com/**');
    
    console.log('✅ API超时处理测试通过');
  });

  test('网络恢复后正常工作测试', async () => {
    // 首先模拟网络错误
    await helpers.simulateNetworkError();
    
    // 发送一条消息触发网络错误
    await helpers.safeType(SELECTORS.chat.inputField, '网络错误测试消息');
    await helpers.safeClick(SELECTORS.chat.sendButton);
    await page.waitForTimeout(3000);
    
    // 恢复网络连接
    await helpers.restoreNetwork();
    
    // 等待一段时间让系统检测网络恢复
    await page.waitForTimeout(2000);
    
    // 发送新消息测试网络恢复
    const recoveryMessage = '网络恢复测试消息';
    await helpers.safeType(SELECTORS.chat.inputField, recoveryMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待用户消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 等待可能的AI响应
    await page.waitForTimeout(10000);
    
    // 检查是否收到了响应（真实或模拟）
    const aiMessages = page.locator(SELECTORS.chat.aiMessage);
    const aiMessageCount = await aiMessages.count();
    
    // 验证系统仍然能够处理消息
    expect(aiMessageCount).toBeGreaterThan(0);
    
    console.log('✅ 网络恢复后正常工作测试通过');
  });

  test('错误状态显示测试', async () => {
    // 模拟网络错误
    await helpers.simulateNetworkError();
    
    // 发送消息
    await helpers.safeType(SELECTORS.chat.inputField, '错误状态测试');
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待错误处理
    await page.waitForTimeout(5000);
    
    // 检查是否有错误状态指示器
    const errorIndicators = [
      SELECTORS.status.connectionError,
      SELECTORS.status.fallbackMode,
      SELECTORS.error.toast,
      SELECTORS.error.message
    ];
    
    let hasErrorIndicator = false;
    for (const selector of errorIndicators) {
      const element = page.locator(selector);
      if (await element.count() > 0 && await element.isVisible()) {
        hasErrorIndicator = true;
        console.log(`发现错误指示器: ${selector}`);
        break;
      }
    }
    
    // 如果没有UI错误指示器，检查控制台日志
    if (!hasErrorIndicator) {
      const logs = helpers.getConsoleLogs();
      const errorLogs = logs.filter(log => 
        log.text.includes('错误') || 
        log.text.includes('失败') ||
        log.text.includes('网络') ||
        log.text.includes('模拟')
      );
      
      expect(errorLogs.length).toBeGreaterThan(0);
      console.log('✅ 错误状态通过控制台日志正确显示');
    } else {
      console.log('✅ 错误状态通过UI指示器正确显示');
    }
    
    // 恢复网络
    await helpers.restoreNetwork();
    
    console.log('✅ 错误状态显示测试通过');
  });

  test('模拟响应内容测试', async () => {
    // 模拟网络错误
    await helpers.simulateNetworkError();
    
    const testMessage = '请介绍一下人工智能';
    
    // 发送消息
    await helpers.safeType(SELECTORS.chat.inputField, testMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待用户消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 等待模拟响应
    await page.waitForTimeout(8000);
    
    // 检查是否有AI响应
    const aiMessages = page.locator(SELECTORS.chat.aiMessage);
    const aiMessageCount = await aiMessages.count();
    
    if (aiMessageCount > 0) {
      const aiMessage = aiMessages.last();
      const aiContent = await aiMessage.locator(SELECTORS.message.content).textContent();
      
      // 验证模拟响应的质量
      expect(aiContent.length).toBeGreaterThan(10);
      
      // 验证模拟响应包含合理的内容
      const hasReasonableContent = 
        aiContent.includes('人工智能') ||
        aiContent.includes('AI') ||
        aiContent.includes('网络') ||
        aiContent.includes('连接') ||
        aiContent.includes('抱歉') ||
        aiContent.includes('模拟');
      
      expect(hasReasonableContent).toBe(true);
      
      console.log('✅ 模拟响应内容测试通过 - 内容合理');
    } else {
      console.log('⚠️ 未收到模拟响应，但这可能是正常的错误处理行为');
    }
    
    // 恢复网络
    await helpers.restoreNetwork();
    
    // 截图记录
    await helpers.takeScreenshot('mock-response-content');
  });

  test('连续网络错误处理测试', async () => {
    // 模拟网络错误
    await helpers.simulateNetworkError();
    
    // 连续发送多条消息
    const messages = [
      '第一条网络错误消息',
      '第二条网络错误消息',
      '第三条网络错误消息'
    ];
    
    for (const message of messages) {
      await helpers.safeType(SELECTORS.chat.inputField, message);
      await helpers.safeClick(SELECTORS.chat.sendButton);
      await page.waitForTimeout(2000);
    }
    
    // 等待所有消息处理完成
    await page.waitForTimeout(5000);
    
    // 验证所有用户消息都已发送
    const userMessages = page.locator(SELECTORS.chat.userMessage);
    const userMessageCount = await userMessages.count();
    expect(userMessageCount).toBeGreaterThanOrEqual(messages.length);
    
    // 检查系统是否稳定处理了连续的网络错误
    const logs = helpers.getConsoleLogs();
    const errorLogs = logs.filter(log => 
      log.text.includes('网络错误') || 
      log.text.includes('模拟模式')
    );
    
    // 验证系统检测到了网络错误但没有崩溃
    expect(errorLogs.length).toBeGreaterThan(0);
    
    // 验证应用仍然响应
    await helpers.assertElementExists(SELECTORS.app.container, '应用在连续网络错误后应该仍然响应');
    
    // 恢复网络
    await helpers.restoreNetwork();
    
    console.log('✅ 连续网络错误处理测试通过');
  });

  test('网络状态指示器测试', async () => {
    // 检查是否有网络状态指示器
    const statusIndicators = [
      SELECTORS.status.networkStatus,
      SELECTORS.status.apiStatus
    ];
    
    let hasStatusIndicator = false;
    for (const selector of statusIndicators) {
      const element = page.locator(selector);
      if (await element.count() > 0) {
        hasStatusIndicator = true;
        
        // 模拟网络错误并检查状态变化
        await helpers.simulateNetworkError();
        await page.waitForTimeout(2000);
        
        // 发送消息触发网络检测
        await helpers.safeType(SELECTORS.chat.inputField, '状态测试');
        await helpers.safeClick(SELECTORS.chat.sendButton);
        await page.waitForTimeout(3000);
        
        // 恢复网络
        await helpers.restoreNetwork();
        
        console.log(`✅ 网络状态指示器测试通过: ${selector}`);
        break;
      }
    }
    
    if (!hasStatusIndicator) {
      console.log('ℹ️ 未发现网络状态指示器UI，但这不影响核心功能');
    }
    
    console.log('✅ 网络状态指示器测试完成');
  });
});
