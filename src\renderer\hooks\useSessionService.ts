import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useAppStore } from '../store/useAppStore';
import { useDataService } from './useDataService';
import { Session } from '@shared/types';

export const useSessionService = () => {
  const {
    sessions,
    currentSessionId,
    addSession,
    updateSession,
    deleteSession: deleteSessionFromStore,
    setCurrentSession
  } = useAppStore();
  
  const { saveSessions, saveCurrentSession, deleteSessionData } = useDataService();

  // 创建新会话
  const createNewSession = useCallback(async (title?: string) => {
    try {
      const sessionId = uuidv4();
      const now = Date.now();
      
      const newSession: Session = {
        id: sessionId,
        title: title || `新会话 ${Object.keys(sessions).length + 1}`,
        createdAt: now,
        updatedAt: now,
        messageCount: 0
      };

      // 添加到状态
      addSession(newSession);
      
      // 设置为当前会话
      setCurrentSession(sessionId);

      // 保存到存储
      const updatedSessions = {
        ...sessions,
        [sessionId]: newSession
      };
      await saveSessions(updatedSessions);
      await saveCurrentSession(sessionId);

      return sessionId;
    } catch (error) {
      console.error('创建新会话失败:', error);
      throw error;
    }
  }, [sessions, addSession, setCurrentSession, saveSessions, saveCurrentSession]);

  // 删除会话
  const deleteSession = useCallback(async (sessionId: string) => {
    try {
      // 从状态中删除
      deleteSessionFromStore(sessionId);
      
      // 从存储中删除
      await deleteSessionData(sessionId);

      // 如果删除的是当前会话，选择另一个会话或创建新会话
      if (currentSessionId === sessionId) {
        const remainingSessions = Object.values(sessions).filter(s => s.id !== sessionId);
        
        if (remainingSessions.length > 0) {
          // 选择最近更新的会话
          const latestSession = remainingSessions.sort((a, b) => b.updatedAt - a.updatedAt)[0];
          setCurrentSession(latestSession.id);
          await saveCurrentSession(latestSession.id);
        } else {
          // 没有其他会话，清除当前会话
          setCurrentSession(null);
          await saveCurrentSession(null);
        }
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      throw error;
    }
  }, [currentSessionId, sessions, deleteSessionFromStore, deleteSessionData, setCurrentSession, saveCurrentSession]);

  // 重命名会话
  const renameSession = useCallback(async (sessionId: string, newTitle: string) => {
    try {
      const trimmedTitle = newTitle.trim();
      if (!trimmedTitle) {
        throw new Error('会话标题不能为空');
      }

      // 更新状态
      updateSession(sessionId, { 
        title: trimmedTitle,
        updatedAt: Date.now()
      });

      // 保存到存储
      const updatedSessions = {
        ...sessions,
        [sessionId]: {
          ...sessions[sessionId],
          title: trimmedTitle,
          updatedAt: Date.now()
        }
      };
      await saveSessions(updatedSessions);
    } catch (error) {
      console.error('重命名会话失败:', error);
      throw error;
    }
  }, [sessions, updateSession, saveSessions]);

  // 切换会话
  const switchToSession = useCallback(async (sessionId: string) => {
    try {
      if (sessions[sessionId]) {
        setCurrentSession(sessionId);
        await saveCurrentSession(sessionId);
      } else {
        throw new Error('会话不存在');
      }
    } catch (error) {
      console.error('切换会话失败:', error);
      throw error;
    }
  }, [sessions, setCurrentSession, saveCurrentSession]);

  // 复制会话
  const duplicateSession = useCallback(async (sessionId: string) => {
    try {
      const originalSession = sessions[sessionId];
      if (!originalSession) {
        throw new Error('原会话不存在');
      }

      const newSessionId = await createNewSession(`${originalSession.title} - 副本`);
      
      // 这里可以添加复制消息的逻辑，如果需要的话
      
      return newSessionId;
    } catch (error) {
      console.error('复制会话失败:', error);
      throw error;
    }
  }, [sessions, createNewSession]);

  // 获取会话统计信息
  const getSessionStats = useCallback(() => {
    const sessionList = Object.values(sessions);
    const totalSessions = sessionList.length;
    const totalMessages = sessionList.reduce((sum, session) => sum + session.messageCount, 0);
    
    const now = Date.now();
    const oneDayAgo = now - 24 * 60 * 60 * 1000;
    const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;
    
    const recentSessions = sessionList.filter(session => session.updatedAt > oneDayAgo).length;
    const weeklyActiveSessions = sessionList.filter(session => session.updatedAt > oneWeekAgo).length;

    return {
      totalSessions,
      totalMessages,
      recentSessions,
      weeklyActiveSessions
    };
  }, [sessions]);

  // 清理空会话
  const cleanupEmptySessions = useCallback(async () => {
    try {
      const emptySessions = Object.values(sessions).filter(session => session.messageCount === 0);
      
      for (const session of emptySessions) {
        await deleteSession(session.id);
      }
      
      return emptySessions.length;
    } catch (error) {
      console.error('清理空会话失败:', error);
      throw error;
    }
  }, [sessions, deleteSession]);

  return {
    createNewSession,
    deleteSession,
    renameSession,
    switchToSession,
    duplicateSession,
    getSessionStats,
    cleanupEmptySessions
  };
};
