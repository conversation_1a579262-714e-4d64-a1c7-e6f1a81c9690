# 代码风格和约定

## TypeScript配置
- **严格模式**: 启用strict模式，确保类型安全
- **目标版本**: ES2020
- **模块系统**: ESNext + Node解析
- **JSX**: react-jsx模式

## 命名约定
- **文件命名**: 
  - 组件文件使用PascalCase (如 `TitleBar.tsx`)
  - 工具文件使用camelCase (如 `aiService.ts`)
  - 类型文件使用camelCase (如 `types.ts`)
- **变量命名**: camelCase
- **类命名**: PascalCase
- **接口命名**: PascalCase，不使用I前缀
- **常量命名**: UPPER_SNAKE_CASE

## 项目结构约定
- **路径别名**: 使用@符号别名
  - `@/`: src根目录
  - `@main/`: 主进程代码
  - `@renderer/`: 渲染进程代码
  - `@shared/`: 共享代码
- **文件组织**: 按功能模块组织，而非文件类型

## 代码组织
- **类结构**: 使用class组织主进程代码，提供清晰的封装
- **React组件**: 使用函数组件 + Hooks模式
- **状态管理**: 使用Zustand进行状态管理
- **类型定义**: 集中在shared/types.ts中定义

## 注释和文档
- **中文注释**: 使用中文进行代码注释
- **JSDoc**: 对公共API使用JSDoc格式注释
- **README**: 详细的项目文档和使用说明