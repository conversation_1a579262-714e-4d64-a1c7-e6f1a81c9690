// 简化的API测试脚本
const axios = require('axios');

console.log('🚀 开始测试API连接...');

// 直接使用API密钥
const apiKey = 'sk-466900693bb54313bb9c9a5feb986eb4';

console.log('🔑 API密钥:', apiKey.substring(0, 10) + '...');

// 测试API连接
async function testAPI() {
  try {
    console.log('📡 发送API请求...');
    
    const response = await axios.post(
      'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
      {
        model: 'qwen-plus',
        messages: [
          {
            role: 'system',
            content: '你是一个有用的AI助手，请用中文回答用户的问题。'
          },
          {
            role: 'user',
            content: '你好，请简单介绍一下自己'
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        stream: false
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    console.log('✅ API调用成功！');
    console.log('📝 AI响应:', response.data.choices[0].message.content);
    console.log('📊 使用的模型:', response.data.model);
    console.log('🔢 Token使用情况:', response.data.usage);
    
  } catch (error) {
    console.error('❌ API调用失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('其他错误:', error.message);
    }
  }
}

// 运行测试
testAPI().then(() => {
  console.log('🏁 测试完成');
}).catch(err => {
  console.error('💥 测试失败:', err);
});
