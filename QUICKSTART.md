# 桌面AI助手 - 快速开始指南

## 🚀 快速部署

### 1. 环境准备

确保您的系统已安装：
- Node.js 16+ 
- npm 8+
- Windows 10+

### 2. 获取API密钥

1. 访问 [阿里云百炼平台](https://dashscope.aliyuncs.com/)
2. 注册并获取API密钥
3. 将API密钥保存到 `docs/env` 文件中：
   ```
   api-key=your-dashscope-api-key-here
   ```

### 3. 一键安装和运行

```bash
# 克隆项目（如果还没有）
git clone <repository-url>
cd desktop-plugin

# 一键设置和运行
npm run setup
npm run dev
```

### 4. 开发模式

```bash
# 启动开发服务器（在一个终端）
npm run dev

# 启动Electron应用（在另一个终端）
npm start
```

### 5. 构建生产版本

```bash
# 一键构建所有
npm run build-all

# 或分步构建
npm run build      # 构建代码
npm run dist:win   # 打包Windows安装程序
```

## 📋 功能清单

- ✅ **基础架构**: Electron + React + TypeScript
- ✅ **AI对话**: 通义千问API集成，支持流式响应
- ✅ **会话管理**: 多会话支持，历史记录持久化
- ✅ **现代UI**: Ant Design组件库，响应式设计
- ✅ **系统托盘**: 常驻托盘，快速访问
- ✅ **快捷键**: 全局快捷键支持
- ✅ **自动启动**: 开机自启动选项
- ✅ **本地存储**: 安全的本地数据存储
- ✅ **设置界面**: 完整的配置管理
- ✅ **安装程序**: Windows NSIS安装包

## 🎯 使用说明

### 首次使用

1. 启动应用后，点击侧边栏的"设置"按钮
2. 输入您的通义千问API密钥
3. 调整AI参数（可选）
4. 点击"新建会话"开始对话

### 快捷键

- `Ctrl+Shift+A`: 显示/隐藏主窗口
- `Ctrl+Shift+N`: 创建新会话

### 系统托盘

- 左键点击：显示/隐藏窗口
- 右键点击：显示菜单

## 🔧 开发指南

### 项目结构

```
src/
├── main/           # Electron主进程
├── renderer/       # React渲染进程
└── shared/         # 共享代码

assets/             # 静态资源
docs/               # 文档和配置
scripts/            # 构建脚本
```

### 添加新功能

1. 在 `src/shared/types.ts` 定义类型
2. 在主进程实现业务逻辑
3. 在渲染进程实现UI
4. 通过IPC进行通信

### 调试技巧

- 开发模式自动打开DevTools
- 主进程日志在终端查看
- 渲染进程日志在DevTools查看

## 📦 部署说明

### 开发环境

```bash
npm run dev    # 启动开发服务器
npm start      # 启动应用
```

### 生产构建

```bash
npm run build-all  # 一键构建
```

输出文件在 `release/` 目录：
- `.exe` 安装程序
- `-portable.exe` 便携版

### 系统要求

- Windows 10 或更高版本
- 4GB RAM（推荐8GB）
- 100MB 可用磁盘空间

## ❓ 常见问题

### Q: 应用无法启动？
A: 检查Node.js版本，确保依赖正确安装

### Q: AI无法响应？
A: 检查API密钥配置和网络连接

### Q: 托盘图标不显示？
A: 运行 `npm run create-icons` 创建图标

### Q: 如何更新API密钥？
A: 在设置界面更新，或直接修改 `docs/env` 文件

## 🆘 获取帮助

- 查看完整文档：`README.md`
- 提交问题：GitHub Issues
- 功能建议：GitHub Discussions

## 🎉 开始使用

现在您可以开始使用桌面AI助手了！享受与AI的智能对话体验。
