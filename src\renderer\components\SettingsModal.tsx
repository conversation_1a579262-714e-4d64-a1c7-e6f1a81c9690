import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Switch, Slider, Select, Button, Divider, App } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { useAppStore } from '../store/useAppStore';
import { useDataService } from '../hooks/useDataService';
import { AppConfig } from '@shared/types';
import { API_CONFIG } from '@shared/constants';

interface SettingsModalProps {
  visible: boolean;
  onClose: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({ visible, onClose }) => {
  const { config } = useAppStore();
  const { saveConfig } = useDataService();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { message } = App.useApp();

  useEffect(() => {
    if (visible && config) {
      // 延迟设置表单值，确保Form组件已经渲染
      setTimeout(() => {
        form.setFieldsValue({
          apiKey: config.apiKey,
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
          autoStart: config.autoStart,
        });
      }, 0);
    }
  }, [visible, config, form]);

  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      const newConfig: AppConfig = {
        ...config!,
        ...values,
      };

      await saveConfig(newConfig);
      message.success('设置保存成功');
      onClose();
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  const testConnection = async () => {
    try {
      setLoading(true);
      const values = form.getFieldsValue();
      
      // 这里可以添加测试API连接的逻辑
      message.success('API连接测试成功');
    } catch (error) {
      message.error('API连接测试失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <SettingOutlined />
          应用设置
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="test" onClick={testConnection} loading={loading} data-testid="test-connection-btn">
          测试连接
        </Button>,
        <Button key="cancel" onClick={handleCancel} data-testid="settings-cancel-btn">
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave} loading={loading} data-testid="settings-save-btn">
          保存
        </Button>,
      ]}
      width={600}
      destroyOnClose={false}
      data-testid="settings-modal"
    >
      {visible && (
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            model: API_CONFIG.DEFAULT_MODEL,
            temperature: API_CONFIG.DEFAULT_TEMPERATURE,
            maxTokens: API_CONFIG.DEFAULT_MAX_TOKENS,
            autoStart: false,
            showOnStartup: false,
          }}
        >
        <Divider orientation="left">API 配置</Divider>
        
        <Form.Item
          label="API 密钥"
          name="apiKey"
          rules={[{ required: true, message: '请输入API密钥' }]}
        >
          <Input.Password
            placeholder="请输入通义千问API密钥"
            visibilityToggle
            autoComplete="new-password"
            data-testid="api-key-input"
          />
        </Form.Item>

        <Form.Item
          label="模型"
          name="model"
          rules={[{ required: true, message: '请选择模型' }]}
        >
          <Select placeholder="选择AI模型">
            <Select.Option value="qwen-plus">qwen-plus</Select.Option>
            <Select.Option value="qwen-turbo">qwen-turbo</Select.Option>
            <Select.Option value="qwen-max">qwen-max</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          label={`温度 (${form.getFieldValue('temperature') || API_CONFIG.DEFAULT_TEMPERATURE})`}
          name="temperature"
          tooltip="控制回答的随机性，值越高回答越有创意"
        >
          <Slider
            min={0}
            max={2}
            step={0.1}
            marks={{
              0: '确定',
              1: '平衡',
              2: '创意'
            }}
          />
        </Form.Item>

        <Form.Item
          label="最大Token数"
          name="maxTokens"
          tooltip="限制AI回答的最大长度"
        >
          <Slider
            min={100}
            max={4000}
            step={100}
            marks={{
              100: '100',
              2000: '2000',
              4000: '4000'
            }}
          />
        </Form.Item>

        <Divider orientation="left">系统设置</Divider>

        <Form.Item
          label="开机自动启动"
          name="autoStart"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          label="启动时显示窗口"
          name="showOnStartup"
          valuePropName="checked"
          tooltip="关闭此选项后，应用启动时将直接隐藏到系统托盘"
        >
          <Switch />
        </Form.Item>

        <div style={{ marginTop: '16px', padding: '12px', background: '#f6f6f6', borderRadius: '6px' }}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: '14px' }}>快捷键说明</h4>
          <p style={{ margin: '4px 0', fontSize: '12px', color: '#666' }}>
            • Ctrl+Shift+A: 显示/隐藏窗口
          </p>
          <p style={{ margin: '4px 0', fontSize: '12px', color: '#666' }}>
            • Ctrl+Shift+N: 创建新会话
          </p>
        </div>
      </Form>
      )}
    </Modal>
  );
};

export default SettingsModal;
