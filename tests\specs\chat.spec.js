/**
 * 聊天功能自动化测试
 * 测试消息发送、AI响应、流式显示等聊天核心功能
 */

const { test, expect } = require('@playwright/test');
const { _electron: electron } = require('playwright');
const path = require('path');
const { createTestHelpers } = require('../utils/helpers');
const { SELECTORS } = require('../utils/selectors');

test.describe('聊天功能测试', () => {
  let electronApp;
  let page;
  let helpers;

  test.beforeAll(async () => {
    // 启动Electron应用
    electronApp = await electron.launch({
      args: [path.join(__dirname, '../../dist/main/main.js')],
      env: {
        NODE_ENV: 'test',
        ELECTRON_IS_DEV: '1'
      }
    });

    page = await electronApp.firstWindow();
    helpers = createTestHelpers(page);
    helpers.startConsoleLogging();
    
    await page.waitForLoadState('domcontentloaded');
    await helpers.waitForLoad();
    
    // 创建一个新会话用于测试
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
  });

  test.afterAll(async () => {
    if (helpers) {
      await helpers.cleanup();
    }
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('消息发送基础功能测试', async () => {
    const testMessage = '你好，这是一条测试消息';
    
    // 在输入框中输入消息
    await helpers.safeType(SELECTORS.chat.inputField, testMessage);
    
    // 验证输入框内容
    const inputValue = await page.locator(SELECTORS.chat.inputField).inputValue();
    expect(inputValue).toBe(testMessage);
    
    // 点击发送按钮
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待用户消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 验证用户消息显示正确
    const userMessage = page.locator(SELECTORS.chat.userMessage).last();
    await helpers.assertTextContent(userMessage.locator(SELECTORS.message.content), testMessage);
    
    // 验证输入框已清空
    const clearedInputValue = await page.locator(SELECTORS.chat.inputField).inputValue();
    expect(clearedInputValue).toBe('');
    
    // 截图记录
    await helpers.takeScreenshot('message-sent');
    
    console.log('✅ 消息发送基础功能测试通过');
  });

  test('AI响应功能测试', async () => {
    const testMessage = '请简单介绍一下你自己';
    
    // 发送消息
    await helpers.safeType(SELECTORS.chat.inputField, testMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待用户消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 等待AI响应（可能需要较长时间）
    try {
      await helpers.waitForAIResponse(30000);
      
      // 验证AI消息存在
      await helpers.assertElementExists(SELECTORS.chat.aiMessage, 'AI响应消息应该存在');
      
      // 验证AI消息有内容
      const aiMessage = page.locator(SELECTORS.chat.aiMessage).last();
      const aiContent = await aiMessage.locator(SELECTORS.message.content).textContent();
      expect(aiContent.length).toBeGreaterThan(0);
      
      // 截图记录
      await helpers.takeScreenshot('ai-response-received');
      
      console.log('✅ AI响应功能测试通过 - 收到真实API响应');
      
    } catch (error) {
      // 如果AI响应超时，检查是否切换到了模拟模式
      console.log('⚠️ AI响应超时，检查模拟模式...');
      
      // 等待可能的模拟响应
      await page.waitForTimeout(5000);
      
      // 检查是否有AI消息（模拟或真实）
      const aiMessages = page.locator(SELECTORS.chat.aiMessage);
      const aiMessageCount = await aiMessages.count();
      
      if (aiMessageCount > 0) {
        console.log('✅ AI响应功能测试通过 - 收到模拟响应');
        await helpers.takeScreenshot('ai-response-fallback');
      } else {
        // 检查控制台日志中的错误信息
        const logs = helpers.getConsoleLogs();
        const networkErrors = logs.filter(log => 
          log.text.includes('网络错误') || 
          log.text.includes('模拟模式') ||
          log.text.includes('API调用失败')
        );
        
        if (networkErrors.length > 0) {
          console.log('⚠️ 检测到网络问题，但这是预期的fallback行为');
          console.log('✅ AI响应功能测试通过 - 网络异常处理正常');
        } else {
          throw new Error('AI响应功能异常：既没有收到响应也没有错误处理');
        }
      }
    }
  });

  test('Enter键发送消息测试', async () => {
    const testMessage = '测试Enter键发送';
    
    // 在输入框中输入消息
    await helpers.safeType(SELECTORS.chat.inputField, testMessage);
    
    // 按Enter键发送
    await page.locator(SELECTORS.chat.inputField).press('Enter');
    
    // 等待用户消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 验证消息发送成功
    const userMessages = page.locator(SELECTORS.chat.userMessage);
    const lastUserMessage = userMessages.last();
    await helpers.assertTextContent(lastUserMessage.locator(SELECTORS.message.content), testMessage);
    
    console.log('✅ Enter键发送消息测试通过');
  });

  test('Shift+Enter换行功能测试', async () => {
    const multiLineMessage = '第一行\n第二行\n第三行';
    
    // 清空输入框
    await page.locator(SELECTORS.chat.inputField).clear();
    
    // 输入第一行
    await page.locator(SELECTORS.chat.inputField).type('第一行');
    
    // 按Shift+Enter换行
    await page.locator(SELECTORS.chat.inputField).press('Shift+Enter');
    
    // 输入第二行
    await page.locator(SELECTORS.chat.inputField).type('第二行');
    
    // 再次换行
    await page.locator(SELECTORS.chat.inputField).press('Shift+Enter');
    
    // 输入第三行
    await page.locator(SELECTORS.chat.inputField).type('第三行');
    
    // 验证输入框内容包含换行
    const inputValue = await page.locator(SELECTORS.chat.inputField).inputValue();
    expect(inputValue).toContain('\n');
    
    // 发送消息
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待消息出现并验证多行内容
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    const lastMessage = page.locator(SELECTORS.chat.userMessage).last();
    const messageContent = await lastMessage.locator(SELECTORS.message.content).textContent();
    
    expect(messageContent).toContain('第一行');
    expect(messageContent).toContain('第二行');
    expect(messageContent).toContain('第三行');
    
    console.log('✅ Shift+Enter换行功能测试通过');
  });

  test('发送按钮状态测试', async () => {
    // 清空输入框
    await page.locator(SELECTORS.chat.inputField).clear();
    
    // 验证空输入时发送按钮禁用
    const sendButton = page.locator(SELECTORS.chat.sendButton);
    await expect(sendButton).toBeDisabled();
    
    // 输入内容
    await page.locator(SELECTORS.chat.inputField).type('测试内容');
    
    // 验证有内容时发送按钮启用
    await expect(sendButton).toBeEnabled();
    
    // 清空内容
    await page.locator(SELECTORS.chat.inputField).clear();
    
    // 验证清空后发送按钮再次禁用
    await expect(sendButton).toBeDisabled();
    
    console.log('✅ 发送按钮状态测试通过');
  });

  test('消息显示格式测试', async () => {
    const testMessage = '这是一条测试消息，用于验证显示格式';
    
    // 发送消息
    await helpers.safeType(SELECTORS.chat.inputField, testMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 获取最新的用户消息
    const userMessage = page.locator(SELECTORS.chat.userMessage).last();
    
    // 验证消息结构
    await helpers.assertElementExists(userMessage.locator(SELECTORS.message.content), '消息内容应该存在');
    
    // 验证消息内容正确
    await helpers.assertTextContent(userMessage.locator(SELECTORS.message.content), testMessage);
    
    // 验证消息有时间戳（如果实现了）
    const timestamp = userMessage.locator(SELECTORS.message.timestamp);
    if (await timestamp.count() > 0) {
      const timestampText = await timestamp.textContent();
      expect(timestampText.length).toBeGreaterThan(0);
    }
    
    console.log('✅ 消息显示格式测试通过');
  });

  test('消息列表滚动测试', async () => {
    // 发送多条消息以测试滚动
    for (let i = 1; i <= 5; i++) {
      await helpers.safeType(SELECTORS.chat.inputField, `测试消息 ${i}`);
      await helpers.safeClick(SELECTORS.chat.sendButton);
      await page.waitForTimeout(500);
    }
    
    // 验证消息列表存在
    await helpers.assertElementExists(SELECTORS.chat.messageList, '消息列表应该存在');
    
    // 验证有多条消息
    const userMessages = page.locator(SELECTORS.chat.userMessage);
    const messageCount = await userMessages.count();
    expect(messageCount).toBeGreaterThanOrEqual(5);
    
    // 验证最新消息可见（自动滚动到底部）
    const lastMessage = userMessages.last();
    await expect(lastMessage).toBeVisible();
    
    console.log('✅ 消息列表滚动测试通过');
  });

  test('长消息处理测试', async () => {
    // 创建一条很长的消息
    const longMessage = '这是一条非常长的测试消息。'.repeat(50);
    
    // 发送长消息
    await helpers.safeType(SELECTORS.chat.inputField, longMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    
    // 等待消息出现
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 验证长消息正确显示
    const lastMessage = page.locator(SELECTORS.chat.userMessage).last();
    const messageContent = await lastMessage.locator(SELECTORS.message.content).textContent();
    expect(messageContent).toBe(longMessage);
    
    // 验证消息容器没有溢出
    const messageContainer = lastMessage.locator(SELECTORS.message.content);
    const boundingBox = await messageContainer.boundingBox();
    expect(boundingBox.width).toBeLessThan(1000); // 假设最大宽度限制
    
    console.log('✅ 长消息处理测试通过');
  });
});
