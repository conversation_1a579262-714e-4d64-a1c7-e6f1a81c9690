/**
 * 桌面AI助手自动化测试运行脚本
 * 启动测试环境并运行完整的测试套件
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      startTime: null,
      endTime: null,
      details: []
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = {
      info: '📋',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      process: '🔄'
    }[type] || '📋';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  async checkPrerequisites() {
    this.log('检查测试前置条件...', 'process');
    
    // 检查必要文件
    const requiredFiles = [
      'playwright.config.js',
      'tests/setup/global-setup.js',
      'tests/setup/global-teardown.js',
      'tests/specs/basic.spec.js',
      'tests/specs/chat.spec.js',
      'tests/specs/session.spec.js',
      'tests/specs/network.spec.js'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`缺少必要文件: ${file}`);
      }
    }

    // 检查Playwright是否安装
    if (!fs.existsSync('node_modules/@playwright/test')) {
      throw new Error('Playwright未安装，请运行: npm install @playwright/test');
    }

    // 检查应用是否已构建
    if (!fs.existsSync('dist/main/main.js')) {
      this.log('应用未构建，开始构建...', 'warning');
      await this.buildApplication();
    }

    this.log('测试前置条件检查完成', 'success');
  }

  async buildApplication() {
    this.log('构建应用...', 'process');
    
    try {
      // 构建主进程
      await this.runCommand('npx', ['webpack', '--config', 'webpack.main.config.js'], {
        description: '构建主进程'
      });

      // 构建渲染进程
      await this.runCommand('npx', ['webpack', '--config', 'webpack.renderer.config.js', '--mode', 'production'], {
        description: '构建渲染进程'
      });

      this.log('应用构建完成', 'success');
    } catch (error) {
      throw new Error(`应用构建失败: ${error.message}`);
    }
  }

  async runCommand(command, args, options = {}) {
    const { description, timeout = 60000 } = options;
    
    if (description) {
      this.log(description, 'process');
    }

    return new Promise((resolve, reject) => {
      const child = spawn(command, args, {
        stdio: 'pipe',
        shell: true
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`命令执行失败，退出码: ${code}\n${stderr}`));
        }
      });

      if (timeout) {
        setTimeout(() => {
          child.kill();
          reject(new Error('命令执行超时'));
        }, timeout);
      }
    });
  }

  async runPlaywrightTests() {
    this.log('开始运行Playwright测试...', 'process');
    this.testResults.startTime = new Date();

    try {
      const result = await this.runCommand('npx', ['playwright', 'test', '--reporter=json'], {
        description: '执行Playwright测试套件',
        timeout: 300000 // 5分钟超时
      });

      this.testResults.endTime = new Date();
      this.testResults.duration = this.testResults.endTime - this.testResults.startTime;

      // 解析测试结果
      this.parseTestResults(result.stdout);
      
      this.log('Playwright测试执行完成', 'success');
      return result;

    } catch (error) {
      this.testResults.endTime = new Date();
      this.testResults.duration = this.testResults.endTime - this.testResults.startTime;
      
      this.log(`测试执行失败: ${error.message}`, 'error');
      
      // 尝试解析部分结果
      if (error.message.includes('stdout')) {
        try {
          this.parseTestResults(error.stdout || '');
        } catch (parseError) {
          this.log('无法解析测试结果', 'warning');
        }
      }
      
      throw error;
    }
  }

  parseTestResults(stdout) {
    try {
      // 查找JSON结果
      const jsonMatch = stdout.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const results = JSON.parse(jsonMatch[0]);
        
        if (results.stats) {
          this.testResults.total = results.stats.total || 0;
          this.testResults.passed = results.stats.passed || 0;
          this.testResults.failed = results.stats.failed || 0;
          this.testResults.skipped = results.stats.skipped || 0;
        }

        if (results.suites) {
          this.testResults.details = this.extractTestDetails(results.suites);
        }
      }
    } catch (error) {
      this.log('解析测试结果失败，使用默认统计', 'warning');
    }
  }

  extractTestDetails(suites) {
    const details = [];
    
    const processSuite = (suite) => {
      if (suite.tests) {
        suite.tests.forEach(test => {
          details.push({
            title: test.title,
            status: test.outcome,
            duration: test.duration,
            file: suite.file
          });
        });
      }
      
      if (suite.suites) {
        suite.suites.forEach(processSuite);
      }
    };

    suites.forEach(processSuite);
    return details;
  }

  generateReport() {
    this.log('生成测试报告...', 'process');

    const report = {
      summary: {
        total: this.testResults.total,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        skipped: this.testResults.skipped,
        successRate: this.testResults.total > 0 ? 
          ((this.testResults.passed / this.testResults.total) * 100).toFixed(2) + '%' : '0%',
        duration: `${(this.testResults.duration / 1000).toFixed(2)}秒`,
        startTime: this.testResults.startTime?.toISOString(),
        endTime: this.testResults.endTime?.toISOString()
      },
      details: this.testResults.details,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        timestamp: new Date().toISOString()
      }
    };

    // 保存JSON报告
    const reportDir = 'test-results';
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    fs.writeFileSync(
      path.join(reportDir, 'test-report.json'),
      JSON.stringify(report, null, 2)
    );

    // 生成简单的文本报告
    const textReport = this.generateTextReport(report);
    fs.writeFileSync(
      path.join(reportDir, 'test-report.txt'),
      textReport
    );

    this.log('测试报告已生成', 'success');
    return report;
  }

  generateTextReport(report) {
    const lines = [
      '🧪 桌面AI助手自动化测试报告',
      '=' .repeat(50),
      '',
      '📊 测试统计:',
      `  总测试数: ${report.summary.total}`,
      `  通过: ${report.summary.passed}`,
      `  失败: ${report.summary.failed}`,
      `  跳过: ${report.summary.skipped}`,
      `  成功率: ${report.summary.successRate}`,
      `  执行时间: ${report.summary.duration}`,
      '',
      '📋 测试详情:',
    ];

    if (report.details && report.details.length > 0) {
      report.details.forEach(test => {
        const status = test.status === 'passed' ? '✅' : 
                     test.status === 'failed' ? '❌' : '⏭️';
        lines.push(`  ${status} ${test.title} (${test.duration}ms)`);
      });
    } else {
      lines.push('  无详细测试信息');
    }

    lines.push('');
    lines.push('🔧 环境信息:');
    lines.push(`  Node.js版本: ${report.environment.nodeVersion}`);
    lines.push(`  平台: ${report.environment.platform}`);
    lines.push(`  测试时间: ${report.environment.timestamp}`);
    lines.push('');
    lines.push('=' .repeat(50));

    return lines.join('\n');
  }

  printSummary(report) {
    console.log('\n' + '='.repeat(60));
    console.log('🎉 桌面AI助手自动化测试完成！');
    console.log('='.repeat(60));
    console.log('');
    console.log('📊 测试结果统计:');
    console.log(`  📋 总测试数: ${report.summary.total}`);
    console.log(`  ✅ 通过: ${report.summary.passed}`);
    console.log(`  ❌ 失败: ${report.summary.failed}`);
    console.log(`  ⏭️ 跳过: ${report.summary.skipped}`);
    console.log(`  📈 成功率: ${report.summary.successRate}`);
    console.log(`  ⏱️ 执行时间: ${report.summary.duration}`);
    console.log('');
    
    if (this.testResults.failed > 0) {
      console.log('❌ 失败的测试:');
      report.details
        .filter(test => test.status === 'failed')
        .forEach(test => {
          console.log(`  • ${test.title}`);
        });
      console.log('');
    }

    console.log('📁 详细报告位置:');
    console.log('  • JSON报告: test-results/test-report.json');
    console.log('  • 文本报告: test-results/test-report.txt');
    console.log('  • HTML报告: test-results/html-report/index.html');
    console.log('');
    console.log('='.repeat(60));
  }

  async run() {
    try {
      this.log('🚀 启动桌面AI助手自动化测试', 'info');
      
      // 检查前置条件
      await this.checkPrerequisites();
      
      // 运行测试
      await this.runPlaywrightTests();
      
      // 生成报告
      const report = this.generateReport();
      
      // 打印总结
      this.printSummary(report);
      
      // 根据测试结果决定退出码
      const exitCode = this.testResults.failed > 0 ? 1 : 0;
      process.exit(exitCode);
      
    } catch (error) {
      this.log(`测试运行失败: ${error.message}`, 'error');
      
      // 尝试生成部分报告
      try {
        const report = this.generateReport();
        this.printSummary(report);
      } catch (reportError) {
        this.log('无法生成测试报告', 'error');
      }
      
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const runner = new TestRunner();
  runner.run();
}

module.exports = TestRunner;
