# 技术栈详情

## 前端技术
- **React 18**: 现代化前端框架，使用函数组件和Hooks
- **TypeScript 5.3.3**: 强类型语言，提供类型安全
- **Ant Design 5**: UI组件库，提供现代化界面组件
- **Zustand**: 轻量级状态管理库

## 桌面技术
- **Electron 28**: 跨平台桌面应用框架
- **Webpack 5**: 模块打包工具，支持热重载
- **electron-builder**: 应用打包和分发工具

## 开发工具
- **ESLint**: 代码质量检查工具
- **TypeScript Compiler**: TypeScript编译器
- **ts-loader**: Webpack的TypeScript加载器
- **css-loader & style-loader**: CSS处理工具

## 运行时依赖
- **axios**: HTTP客户端，用于API调用
- **electron-store**: 本地数据持久化
- **auto-launch**: 开机自启动功能
- **uuid**: 唯一标识符生成

## 构建和部署
- **concurrently**: 并发运行多个命令
- **rimraf**: 跨平台文件删除工具
- **NSIS**: Windows安装程序生成器