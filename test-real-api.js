// 真实API测试脚本
const axios = require('axios');

console.log('🚀 开始测试通义千问API...');

// API配置
const API_CONFIG = {
  BASE_URL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  MODEL: 'qwen-plus',
  API_KEY: 'sk-466900693bb54313bb9c9a5feb986eb4'
};

console.log('🔑 使用API密钥:', API_CONFIG.API_KEY.substring(0, 10) + '...');

// 测试非流式API
async function testNormalAPI() {
  console.log('\n📡 测试非流式API...');
  
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/chat/completions`,
      {
        model: API_CONFIG.MODEL,
        messages: [
          {
            role: 'system',
            content: '你是一个有用的AI助手，请用中文回答用户的问题。'
          },
          {
            role: 'user',
            content: '你好，请简单介绍一下自己'
          }
        ],
        temperature: 0.7,
        max_tokens: 500,
        stream: false
      },
      {
        headers: {
          'Authorization': `Bearer ${API_CONFIG.API_KEY}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    console.log('✅ 非流式API调用成功！');
    console.log('📝 AI响应:', response.data.choices[0].message.content);
    console.log('📊 使用的模型:', response.data.model);
    console.log('🔢 Token使用情况:', response.data.usage);
    return true;
    
  } catch (error) {
    console.error('❌ 非流式API调用失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('其他错误:', error.message);
    }
    return false;
  }
}

// 测试流式API
async function testStreamAPI() {
  console.log('\n🌊 测试流式API...');
  
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/chat/completions`,
      {
        model: API_CONFIG.MODEL,
        messages: [
          {
            role: 'system',
            content: '你是一个有用的AI助手，请用中文回答用户的问题。'
          },
          {
            role: 'user',
            content: '请用一段话介绍人工智能的发展历程'
          }
        ],
        temperature: 0.7,
        max_tokens: 800,
        stream: true
      },
      {
        headers: {
          'Authorization': `Bearer ${API_CONFIG.API_KEY}`,
          'Content-Type': 'application/json'
        },
        timeout: 60000,
        responseType: 'stream'
      }
    );

    console.log('✅ 流式API连接成功！');
    console.log('📡 开始接收流式数据...');
    
    let fullMessage = '';
    let chunkCount = 0;

    return new Promise((resolve, reject) => {
      response.data.on('data', (chunk) => {
        const lines = chunk.toString().split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            
            if (data === '[DONE]') {
              console.log('\n🏁 流式响应完成！');
              console.log('📝 完整响应:', fullMessage);
              console.log('📊 总共接收到', chunkCount, '个数据块');
              resolve(true);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              
              if (content) {
                fullMessage += content;
                chunkCount++;
                process.stdout.write(content); // 实时显示内容
              }
            } catch (parseError) {
              // 忽略解析错误
            }
          }
        }
      });

      response.data.on('error', (error) => {
        console.error('\n❌ 流式响应错误:', error);
        reject(false);
      });

      response.data.on('end', () => {
        if (fullMessage) {
          console.log('\n✅ 流式API测试完成！');
          resolve(true);
        } else {
          console.log('\n⚠️ 流式API未收到完整响应');
          resolve(false);
        }
      });
    });
    
  } catch (error) {
    console.error('❌ 流式API调用失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('其他错误:', error.message);
    }
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🧪 开始API功能测试...\n');
  
  const normalResult = await testNormalAPI();
  const streamResult = await testStreamAPI();
  
  console.log('\n📋 测试结果总结:');
  console.log('非流式API:', normalResult ? '✅ 通过' : '❌ 失败');
  console.log('流式API:', streamResult ? '✅ 通过' : '❌ 失败');
  
  if (normalResult && streamResult) {
    console.log('\n🎉 所有API测试通过！后端AI服务可以正常工作。');
  } else {
    console.log('\n⚠️ 部分API测试失败，请检查网络连接和API配置。');
  }
}

// 运行测试
runTests().catch(error => {
  console.error('💥 测试执行失败:', error);
});
