/**
 * Playwright配置文件 - 桌面AI助手自动化测试
 * 配置Electron应用的自动化测试环境
 */

const { defineConfig, devices } = require('@playwright/test');
const path = require('path');

module.exports = defineConfig({
  // 测试目录
  testDir: './tests',
  
  // 全局超时设置
  timeout: 60000,
  expect: {
    timeout: 10000
  },
  
  // 测试失败时的重试次数
  retries: process.env.CI ? 2 : 1,
  
  // 并行执行的worker数量
  workers: 1, // Electron应用通常需要串行执行
  
  // 报告配置
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list']
  ],
  
  // 输出目录
  outputDir: 'test-results/artifacts',
  
  // 全局设置
  use: {
    // 截图设置
    screenshot: 'only-on-failure',
    
    // 视频录制
    video: 'retain-on-failure',
    
    // 跟踪设置
    trace: 'retain-on-failure',
    
    // 操作超时
    actionTimeout: 10000,
    
    // 导航超时
    navigationTimeout: 30000
  },

  // 项目配置
  projects: [
    {
      name: 'electron-tests',
      use: {
        // Electron特定配置
        ...devices['Desktop Chrome'],
        // 自定义Electron配置
        launchOptions: {
          executablePath: process.env.ELECTRON_PATH || require('electron'),
          args: [
            path.join(__dirname, 'dist/main/main.js'),
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security'
          ],
          env: {
            NODE_ENV: 'test',
            ELECTRON_IS_DEV: '1'
          }
        }
      },
      testMatch: 'tests/**/*.spec.js'
    }
  ],

  // 全局设置和清理
  globalSetup: require.resolve('./tests/setup/global-setup.js'),
  globalTeardown: require.resolve('./tests/setup/global-teardown.js'),

  // Web服务器配置（如果需要）
  webServer: {
    command: 'npm run dev:renderer',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 30000
  }
});
