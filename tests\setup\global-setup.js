/**
 * Playwright全局设置
 * 在所有测试开始前启动测试环境
 */

const TestEnvironmentManager = require('../../scripts/start-test-environment');

async function globalSetup(config) {
  console.log('🚀 启动全局测试环境...');
  
  try {
    // 创建测试环境管理器
    const envManager = new TestEnvironmentManager();
    
    // 启动测试环境
    const result = await envManager.start();
    
    // 将环境信息保存到全局状态
    process.env.TEST_DEV_SERVER_URL = result.devServerUrl;
    process.env.TEST_ENVIRONMENT_READY = 'true';
    
    // 保存管理器实例以便清理时使用
    global.__TEST_ENV_MANAGER__ = envManager;
    
    console.log('✅ 全局测试环境启动成功');
    
    // 等待一段时间确保所有服务都完全启动
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    return result;
    
  } catch (error) {
    console.error('❌ 全局测试环境启动失败:', error.message);
    throw error;
  }
}

module.exports = globalSetup;
