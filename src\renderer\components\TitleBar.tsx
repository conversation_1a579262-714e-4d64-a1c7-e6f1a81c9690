import React from 'react';
import { Typography, Space, Button } from 'antd';
import { MinusOutlined, BorderOutlined, CloseOutlined, MenuOutlined } from '@ant-design/icons';
import { useAppStore } from '../store/useAppStore';

const { Text } = Typography;

const TitleBar: React.FC = () => {
  const { toggleSidebar } = useAppStore();

  const handleMinimize = () => {
    window.electronAPI.window.minimize();
  };

  const handleMaximize = () => {
    window.electronAPI.window.maximize();
  };

  const handleClose = () => {
    window.electronAPI.window.close();
  };

  return (
    <div className="title-bar">
      <Space align="center">
        <Button
          type="text"
          icon={<MenuOutlined />}
          size="small"
          onClick={toggleSidebar}
          className="no-drag"
          style={{ color: '#666' }}
        />
        <Text strong style={{ color: '#333', fontSize: '13px' }}>
          桌面AI助手
        </Text>
      </Space>

      <div className="title-bar-buttons">
        <button
          className="title-bar-button minimize"
          onClick={handleMinimize}
          title="最小化"
        />
        <button
          className="title-bar-button maximize"
          onClick={handleMaximize}
          title="最大化"
        />
        <button
          className="title-bar-button close"
          onClick={handleClose}
          title="关闭"
        />
      </div>
    </div>
  );
};

export default TitleBar;
