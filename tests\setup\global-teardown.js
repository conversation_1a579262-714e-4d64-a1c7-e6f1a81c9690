/**
 * Playwright全局清理
 * 在所有测试结束后清理测试环境
 */

async function globalTeardown(config) {
  console.log('🧹 开始清理全局测试环境...');
  
  try {
    // 获取保存的环境管理器实例
    const envManager = global.__TEST_ENV_MANAGER__;
    
    if (envManager) {
      await envManager.shutdown();
      console.log('✅ 测试环境清理完成');
    } else {
      console.log('⚠️ 未找到测试环境管理器实例');
    }
    
    // 清理环境变量
    delete process.env.TEST_DEV_SERVER_URL;
    delete process.env.TEST_ENVIRONMENT_READY;
    
  } catch (error) {
    console.error('❌ 测试环境清理失败:', error.message);
  }
}

module.exports = globalTeardown;
