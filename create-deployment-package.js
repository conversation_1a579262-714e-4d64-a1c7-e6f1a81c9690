// 创建部署包
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const packageName = '桌面AI助手-v1.0.0-部署包';
const deployDir = path.join(__dirname, 'deploy', packageName);

// 创建部署目录
if (fs.existsSync(deployDir)) {
  fs.rmSync(deployDir, { recursive: true, force: true });
}
fs.mkdirSync(deployDir, { recursive: true });

console.log('正在创建部署包...');

// 复制exe文件和相关资源
const sourceDir = path.join(__dirname, 'release', 'win-unpacked');
if (fs.existsSync(sourceDir)) {
  console.log('复制应用程序文件...');
  
  // 复制所有文件
  const copyCommand = `xcopy "${sourceDir}" "${deployDir}" /E /I /H /Y`;
  try {
    execSync(copyCommand, { stdio: 'inherit' });
    console.log('应用程序文件复制完成');
  } catch (error) {
    console.error('复制文件失败:', error.message);
  }
} else {
  console.error('未找到构建输出目录，请先运行构建命令');
  process.exit(1);
}

// 创建启动脚本
const startScript = `@echo off
echo 启动桌面AI助手...
start "" "桌面AI助手.exe"
`;

fs.writeFileSync(path.join(deployDir, '启动.bat'), startScript, 'utf8');

// 创建说明文件
const readme = `# 桌面AI助手 v1.0.0

## 安装说明

1. 将整个文件夹复制到您希望安装的位置
2. 双击"启动.bat"或直接运行"桌面AI助手.exe"

## 功能特性

- 智能AI对话
- 系统托盘集成
- 快捷键支持
- 自动启动选项

## 系统要求

- Windows 10 或更高版本
- 64位系统

## 使用说明

1. 首次启动时，应用会出现在系统托盘中
2. 点击托盘图标可以打开聊天界面
3. 支持快捷键 Ctrl+Shift+A 快速唤起
4. 可在设置中配置开机自启动

## 技术支持

如有问题，请联系技术支持。

---
构建时间: ${new Date().toLocaleString('zh-CN')}
版本: 1.0.0
`;

fs.writeFileSync(path.join(deployDir, 'README.txt'), readme, 'utf8');

// 创建卸载脚本
const uninstallScript = `@echo off
echo 正在停止桌面AI助手...
taskkill /f /im "桌面AI助手.exe" 2>nul
echo 卸载完成。您可以手动删除此文件夹。
pause
`;

fs.writeFileSync(path.join(deployDir, '卸载.bat'), uninstallScript, 'utf8');

console.log(`\n部署包创建完成！`);
console.log(`位置: ${deployDir}`);
console.log(`\n包含文件:`);
console.log(`- 桌面AI助手.exe (主程序)`);
console.log(`- 启动.bat (启动脚本)`);
console.log(`- README.txt (使用说明)`);
console.log(`- 卸载.bat (卸载脚本)`);
console.log(`- 其他运行时文件`);

console.log(`\n您现在可以:`);
console.log(`1. 测试运行: 进入部署目录，双击"启动.bat"`);
console.log(`2. 打包分发: 将整个"${packageName}"文件夹压缩后分发`);
console.log(`3. 直接使用: 将文件夹复制到目标机器上使用`);
