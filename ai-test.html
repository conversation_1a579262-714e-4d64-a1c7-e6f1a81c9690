<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chat-area {
            height: 400px;
            border: 1px solid #ddd;
            padding: 10px;
            overflow-y: auto;
            margin-bottom: 10px;
            background: #fafafa;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 6px;
        }
        .user-message {
            background: #007bff;
            color: white;
            margin-left: 20%;
        }
        .ai-message {
            background: #e9ecef;
            color: #333;
            margin-right: 20%;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI功能测试页面</h1>
        
        <div class="status info" id="status">
            正在初始化AI服务...
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message ai-message">
                👋 你好！我是AI助手。请输入消息开始对话。
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="输入您的消息..." disabled>
            <button id="sendButton" disabled>发送</button>
            <button id="testApiButton">测试API</button>
        </div>
    </div>

    <script>
        const chatArea = document.getElementById('chatArea');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const testApiButton = document.getElementById('testApiButton');
        const status = document.getElementById('status');
        
        let isLoading = false;
        
        // 添加消息到聊天区域
        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            messageDiv.textContent = content;
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
        
        // 更新状态
        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 模拟AI响应
        function simulateAIResponse(userMessage) {
            const responses = [
                `我收到了您的消息："${userMessage}"。这是一个模拟的AI回复。`,
                `感谢您的提问！关于"${userMessage}"，我可以为您提供一些信息。`,
                `您好！我理解您说的"${userMessage}"。让我为您详细解答。`,
                `这是一个很好的问题："${userMessage}"。让我来帮助您。`,
                `关于"${userMessage}"，我有以下建议和信息可以分享。`
            ];
            
            return responses[Math.floor(Math.random() * responses.length)];
        }
        
        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isLoading) return;
            
            // 添加用户消息
            addMessage(message, true);
            messageInput.value = '';
            
            // 设置加载状态
            isLoading = true;
            sendButton.disabled = true;
            messageInput.disabled = true;
            updateStatus('AI正在思考中...', 'info');
            
            try {
                // 检查是否在Electron环境
                if (typeof window.electronAPI !== 'undefined' && window.electronAPI.ai) {
                    updateStatus('使用真实AI服务...', 'info');
                    // 这里应该调用真实的AI API
                    // await window.electronAPI.ai.sendMessage(message, 'test-session');
                    
                    // 暂时使用模拟响应
                    setTimeout(() => {
                        const aiResponse = simulateAIResponse(message);
                        addMessage(aiResponse);
                        updateStatus('✅ AI响应完成', 'success');
                        isLoading = false;
                        sendButton.disabled = false;
                        messageInput.disabled = false;
                    }, 1500);
                } else {
                    updateStatus('使用模拟AI服务...', 'info');
                    // 浏览器环境，使用模拟响应
                    setTimeout(() => {
                        const aiResponse = simulateAIResponse(message);
                        addMessage(aiResponse);
                        updateStatus('✅ 模拟响应完成', 'success');
                        isLoading = false;
                        sendButton.disabled = false;
                        messageInput.disabled = false;
                    }, 1500);
                }
            } catch (error) {
                updateStatus(`❌ 发送失败: ${error.message}`, 'error');
                isLoading = false;
                sendButton.disabled = false;
                messageInput.disabled = false;
            }
        }
        
        // 测试API连接
        async function testAPI() {
            updateStatus('正在测试API连接...', 'info');
            
            try {
                const response = await fetch('/api/test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'test' })
                });
                
                if (response.ok) {
                    updateStatus('✅ API连接正常', 'success');
                } else {
                    updateStatus('⚠️ API连接异常', 'error');
                }
            } catch (error) {
                updateStatus('❌ API连接失败 (这是正常的，因为没有后端API)', 'error');
            }
        }
        
        // 事件监听
        sendButton.addEventListener('click', sendMessage);
        testApiButton.addEventListener('click', testAPI);
        
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 初始化
        setTimeout(() => {
            if (typeof window.electronAPI !== 'undefined') {
                updateStatus('✅ Electron环境已检测到', 'success');
            } else {
                updateStatus('ℹ️ 浏览器环境 - 使用模拟功能', 'info');
            }
            
            messageInput.disabled = false;
            sendButton.disabled = false;
        }, 1000);
    </script>
</body>
</html>
