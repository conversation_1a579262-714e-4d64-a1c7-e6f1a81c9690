/**
 * 测试辅助函数
 * 提供常用的测试工具和方法
 */

const { expect } = require('@playwright/test');

class TestHelpers {
  constructor(page) {
    this.page = page;
  }

  /**
   * 等待元素出现并可见
   */
  async waitForElement(selector, options = {}) {
    const { timeout = 10000 } = options;
    
    await this.page.waitForSelector(selector, {
      state: 'visible',
      timeout
    });
    
    return this.page.locator(selector);
  }

  /**
   * 等待文本内容出现
   */
  async waitForText(text, options = {}) {
    const { timeout = 10000 } = options;
    
    await this.page.waitForFunction(
      (searchText) => document.body.innerText.includes(searchText),
      text,
      { timeout }
    );
  }

  /**
   * 安全点击元素
   */
  async safeClick(selector, options = {}) {
    const element = await this.waitForElement(selector, options);
    await element.click();
    return element;
  }

  /**
   * 安全输入文本
   */
  async safeType(selector, text, options = {}) {
    const element = await this.waitForElement(selector, options);
    await element.clear();
    await element.type(text);
    return element;
  }

  /**
   * 等待加载完成
   */
  async waitForLoad() {
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * 截图并保存
   */
  async takeScreenshot(name) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${name}-${timestamp}.png`;
    
    await this.page.screenshot({
      path: `test-results/screenshots/${filename}`,
      fullPage: true
    });
    
    return filename;
  }

  /**
   * 验证元素存在
   */
  async assertElementExists(selector, message) {
    const element = this.page.locator(selector);
    await expect(element).toBeVisible({ timeout: 10000 });
  }

  /**
   * 验证文本内容
   */
  async assertTextContent(selector, expectedText) {
    const element = this.page.locator(selector);
    await expect(element).toContainText(expectedText);
  }

  /**
   * 等待AI响应
   */
  async waitForAIResponse(timeout = 30000) {
    // 等待AI响应消息出现
    await this.page.waitForSelector('[data-testid="ai-message"]', {
      timeout,
      state: 'visible'
    });
    
    // 等待流式响应完成（检查是否还在打字）
    await this.page.waitForFunction(() => {
      const typingIndicator = document.querySelector('[data-testid="typing-indicator"]');
      return !typingIndicator || typingIndicator.style.display === 'none';
    }, { timeout: 10000 });
  }

  /**
   * 模拟网络错误
   */
  async simulateNetworkError() {
    await this.page.route('**/*dashscope.aliyuncs.com/**', route => {
      route.abort('failed');
    });
  }

  /**
   * 恢复网络连接
   */
  async restoreNetwork() {
    await this.page.unroute('**/*dashscope.aliyuncs.com/**');
  }

  /**
   * 获取控制台日志
   */
  getConsoleLogs() {
    return this.page.consoleLogs || [];
  }

  /**
   * 监听控制台消息
   */
  startConsoleLogging() {
    this.page.consoleLogs = [];
    
    this.page.on('console', msg => {
      this.page.consoleLogs.push({
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      });
    });
  }

  /**
   * 等待特定的控制台消息
   */
  async waitForConsoleMessage(expectedText, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`等待控制台消息超时: ${expectedText}`));
      }, timeout);

      const checkMessage = () => {
        const logs = this.getConsoleLogs();
        const found = logs.find(log => log.text.includes(expectedText));
        
        if (found) {
          clearTimeout(timeoutId);
          resolve(found);
        } else {
          setTimeout(checkMessage, 100);
        }
      };

      checkMessage();
    });
  }

  /**
   * 清理测试数据
   */
  async cleanup() {
    // 清理本地存储
    await this.page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  }
}

/**
 * 创建测试辅助实例
 */
function createTestHelpers(page) {
  return new TestHelpers(page);
}

module.exports = {
  TestHelpers,
  createTestHelpers
};
