@echo off
chcp 65001 >nul
echo 🚀 重新构建最新版桌面AI助手exe
echo.
echo 📊 当前状态：
echo   dist/main/main.js: 2025/7/24 11:55 (最新)
echo   exe文件: 2025/7/24 11:03 (需要更新)
echo.
echo 🔧 开始重新打包...

echo 📦 1. 清理旧的release目录...
if exist release (
    echo 正在删除旧文件...
    rmdir /s /q release 2>nul
    timeout /t 2 /nobreak >nul
)

echo 📦 2. 尝试打包exe文件...
echo 方法1: 使用pack命令...
call npm run pack
if %errorlevel% equ 0 (
    echo ✅ 打包成功！
    goto :success
)

echo ⚠️ 方法1失败，尝试方法2...
call npm run pack-no-sign
if %errorlevel% equ 0 (
    echo ✅ 打包成功！
    goto :success
)

echo ❌ 标准打包方法失败，尝试手动方法...
echo 📦 3. 手动创建便携版...

mkdir portable 2>nul
mkdir portable\dist 2>nul
mkdir portable\assets 2>nul
mkdir portable\node_modules 2>nul

echo 复制构建文件...
xcopy /s /e /y dist portable\dist\ >nul
xcopy /s /e /y assets portable\assets\ >nul
copy package.json portable\ >nul

echo 安装生产依赖...
cd portable
call npm install --production --no-optional >nul 2>&1
cd ..

echo 创建启动脚本...
echo @echo off > portable\start.bat
echo echo 🚀 启动桌面AI助手... >> portable\start.bat
echo echo 应用将启动到后台，按 Ctrl+Shift+A 显示窗口 >> portable\start.bat
echo node_modules\.bin\electron . >> portable\start.bat

echo ✅ 便携版创建完成！
echo 📁 位置: portable\start.bat
goto :end

:success
echo ✅ exe文件重新构建完成！
echo 📁 位置: release\win-unpacked\桌面AI助手.exe
echo.
echo 🆕 新版本包含的功能：
echo   ✅ 启动时隐藏到后台
echo   ✅ 快捷键 Ctrl+Shift+A 显示/隐藏窗口
echo   ✅ 快捷键 Ctrl+Shift+N 新建会话
echo   ✅ 增强的托盘管理（带错误处理）
echo   ✅ 系统通知功能
echo   ✅ 窗口关闭时隐藏而不退出

:end
echo.
echo 🎯 使用说明：
echo 1. 双击exe文件启动（不会显示窗口）
echo 2. 按 Ctrl+Shift+A 显示/隐藏窗口
echo 3. 应用会在屏幕右侧显示
echo.
pause
