import React, { useEffect } from 'react';
import { Layout, App as AntApp } from 'antd';
import TitleBar from './components/TitleBar';
import Sidebar from './components/Sidebar';
import ChatArea from './components/ChatArea';
import { useAppStore } from './store/useAppStore';
import { useDataService } from './hooks/useDataService';
import { useAIService } from './hooks/useAIService';

const { Content } = Layout;

const App: React.FC = () => {
  const { isSidebarOpen } = useAppStore();
  const { loadInitialData } = useDataService();
  const { initializeAI } = useAIService();

  useEffect(() => {
    // 初始化应用
    const initialize = async () => {
      try {
        // 加载初始数据
        await loadInitialData();
        
        // 初始化AI服务
        initializeAI();
        
        console.log('应用初始化完成');
      } catch (error) {
        console.error('应用初始化失败:', error);
      }
    };

    initialize();
  }, []);

  return (
    <AntApp>
      <Layout style={{ height: '100vh', overflow: 'hidden' }} data-testid="app-container">
        {/* 自定义标题栏 */}
        <TitleBar />

        <Layout style={{ height: 'calc(100vh - 32px)' }}>
          {/* 侧边栏 */}
          {isSidebarOpen && (
            <Layout.Sider
              width={280}
              style={{
                background: '#ffffff',
                borderRight: '1px solid #e8e8e8',
              }}
              data-testid="sidebar"
            >
              <Sidebar />
            </Layout.Sider>
          )}

          {/* 主聊天区域 */}
          <Content
            style={{
              background: '#ffffff',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
            }}
            data-testid="chat-container"
          >
            <ChatArea />
          </Content>
        </Layout>
      </Layout>
    </AntApp>
  );
};

export default App;
