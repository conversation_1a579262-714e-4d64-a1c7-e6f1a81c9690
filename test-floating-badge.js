// 测试桌面浮标功能
const { app, BrowserWindow } = require('electron');
const path = require('path');

console.log('🚀 启动浮标功能测试...');

let testWindow = null;
let badgeWindow = null;

function createTestWindow() {
  console.log('📱 创建测试窗口...');
  
  testWindow = new BrowserWindow({
    width: 400,
    height: 300,
    show: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  testWindow.loadURL('data:text/html,<h1>浮标功能测试</h1><p>检查桌面右侧是否出现浮标</p>');
  
  console.log('✅ 测试窗口创建完成');
}

function createFloatingBadge() {
  console.log('🎯 创建桌面浮标...');
  
  const { width, height } = require('electron').screen.getPrimaryDisplay().workAreaSize;
  
  // 浮标尺寸和位置
  const badgeWidth = 60;
  const badgeHeight = 60;
  const x = width - badgeWidth - 20;
  const y = Math.floor(height * 0.3);

  badgeWindow = new BrowserWindow({
    width: badgeWidth,
    height: badgeHeight,
    x: x,
    y: y,
    frame: false,
    show: false,
    resizable: false,
    maximizable: false,
    minimizable: false,
    closable: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    transparent: true,
    hasShadow: false,
    focusable: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
  });

  // 简单的浮标HTML
  const badgeHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body {
          margin: 0;
          padding: 0;
          width: 60px;
          height: 60px;
          background: transparent;
          cursor: pointer;
          user-select: none;
        }
        .badge {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #1677ff, #4096ff);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: 16px;
          box-shadow: 0 4px 12px rgba(22, 119, 255, 0.4);
          transition: all 0.3s ease;
        }
        .badge:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 16px rgba(22, 119, 255, 0.6);
        }
      </style>
    </head>
    <body>
      <div class="badge" onclick="handleClick()">AI</div>
      <script>
        function handleClick() {
          console.log('浮标被点击！');
          // 这里可以发送消息给主进程
        }
      </script>
    </body>
    </html>
  `;

  badgeWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(badgeHTML)}`);
  
  badgeWindow.once('ready-to-show', () => {
    badgeWindow.show();
    badgeWindow.setAlwaysOnTop(true, 'floating');
    console.log('✅ 浮标创建完成并显示');
    console.log(`📍 浮标位置: x=${x}, y=${y}`);
  });

  // 点击事件处理
  badgeWindow.webContents.on('before-input-event', (event, input) => {
    if (input.type === 'mouseDown') {
      console.log('🖱️ 浮标被点击！');
      if (testWindow) {
        testWindow.focus();
      }
    }
  });
}

app.whenReady().then(() => {
  console.log('✅ Electron应用准备就绪');
  
  createTestWindow();
  createFloatingBadge();
  
  console.log('🎯 测试完成！应该看到：');
  console.log('1. 主测试窗口');
  console.log('2. 桌面右侧的蓝色圆形浮标');
  console.log('3. 点击浮标可以聚焦主窗口');
});

app.on('window-all-closed', () => {
  console.log('🔚 所有窗口关闭');
  app.quit();
});

app.on('before-quit', () => {
  console.log('👋 应用即将退出');
  if (badgeWindow) {
    badgeWindow.close();
  }
});
