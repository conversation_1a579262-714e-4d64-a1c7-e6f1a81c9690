// 测试通义千问流式API
const axios = require('axios');
const fs = require('fs');

// 读取API密钥
const envContent = fs.readFileSync('./docs/env', 'utf8');
const apiKey = envContent.split('=')[1].trim();

console.log('🔑 API密钥:', apiKey.substring(0, 10) + '...');

// API配置
const API_CONFIG = {
  BASE_URL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  MODEL: 'qwen-plus',
  TEMPERATURE: 0.7,
  MAX_TOKENS: 2000,
};

// 测试流式API
async function testStreamAPI() {
  try {
    console.log('🚀 开始测试流式API连接...');
    
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/chat/completions`,
      {
        model: API_CONFIG.MODEL,
        messages: [
          {
            role: 'system',
            content: '你是一个有用的AI助手，请用中文回答用户的问题。'
          },
          {
            role: 'user',
            content: '请详细介绍一下人工智能的发展历史，包括主要的里程碑事件'
          }
        ],
        temperature: API_CONFIG.TEMPERATURE,
        max_tokens: API_CONFIG.MAX_TOKENS,
        stream: true // 使用流式响应
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        responseType: 'stream',
        timeout: 30000
      }
    );

    console.log('✅ 流式API连接成功！');
    console.log('📝 AI流式响应:');
    console.log('─'.repeat(50));
    
    let fullMessage = '';
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6).trim();
          
          if (data === '[DONE]') {
            console.log('\n' + '─'.repeat(50));
            console.log('🏁 流式响应完成');
            console.log('📄 完整消息长度:', fullMessage.length, '字符');
            return;
          }

          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices[0]?.delta?.content;
            
            if (content) {
              process.stdout.write(content);
              fullMessage += content;
            }
          } catch (parseError) {
            // 忽略解析错误
          }
        }
      }
    });

    response.data.on('error', (error) => {
      console.error('\n❌ 流式响应错误:', error);
    });
    
  } catch (error) {
    console.error('❌ 流式API调用失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('其他错误:', error.message);
    }
  }
}

// 运行测试
testStreamAPI();
