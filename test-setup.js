/**
 * 测试环境设置验证脚本
 * 验证测试环境是否正确配置
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 验证桌面AI助手测试环境设置...\n');

// 检查必要文件
const requiredFiles = [
  'playwright.config.js',
  'tests/setup/global-setup.js',
  'tests/setup/global-teardown.js',
  'tests/utils/helpers.js',
  'tests/utils/selectors.js',
  'tests/specs/basic.spec.js',
  'tests/specs/chat.spec.js',
  'tests/specs/session.spec.js',
  'tests/specs/network.spec.js',
  'scripts/start-test-environment.js'
];

console.log('📋 检查测试文件...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    allFilesExist = false;
  }
});

// 检查依赖
console.log('\n📦 检查依赖...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const requiredDeps = {
  '@playwright/test': 'devDependencies',
  'playwright': 'devDependencies',
  'electron': 'devDependencies'
};

Object.entries(requiredDeps).forEach(([dep, type]) => {
  if (packageJson[type] && packageJson[type][dep]) {
    console.log(`✅ ${dep} - 已安装`);
  } else {
    console.log(`❌ ${dep} - 未安装`);
    allFilesExist = false;
  }
});

// 检查构建文件
console.log('\n🔨 检查构建文件...');
const buildFiles = [
  'dist/main/main.js',
  'dist/main/preload.js'
];

buildFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`⚠️ ${file} - 需要构建`);
  }
});

// 检查API配置
console.log('\n🔑 检查API配置...');
if (fs.existsSync('docs/env')) {
  const envContent = fs.readFileSync('docs/env', 'utf8');
  const apiKey = envContent.split('=')[1]?.trim();
  if (apiKey && apiKey.length > 10) {
    console.log('✅ API密钥配置正确');
  } else {
    console.log('⚠️ API密钥配置可能有问题');
  }
} else {
  console.log('❌ docs/env 文件不存在');
}

// 检查测试目录结构
console.log('\n📁 检查测试目录结构...');
const testDirs = [
  'tests',
  'tests/setup',
  'tests/specs',
  'tests/utils',
  'tests/reports'
];

testDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir}/`);
  } else {
    console.log(`❌ ${dir}/ - 目录不存在`);
    allFilesExist = false;
  }
});

// 总结
console.log('\n' + '='.repeat(50));
if (allFilesExist) {
  console.log('🎉 测试环境设置验证通过！');
  console.log('\n💡 下一步操作:');
  console.log('1. 确保应用已构建: npm run build');
  console.log('2. 运行测试: node run-tests.js');
  console.log('3. 或者运行单个测试: npx playwright test tests/specs/basic.spec.js');
} else {
  console.log('❌ 测试环境设置不完整');
  console.log('\n🔧 修复建议:');
  console.log('1. 安装缺失的依赖: npm install');
  console.log('2. 创建缺失的目录和文件');
  console.log('3. 构建应用: npm run build');
}
console.log('='.repeat(50));

// 显示测试命令示例
console.log('\n📝 测试命令示例:');
console.log('• 运行所有测试: npx playwright test');
console.log('• 运行基础测试: npx playwright test tests/specs/basic.spec.js');
console.log('• 运行聊天测试: npx playwright test tests/specs/chat.spec.js');
console.log('• 运行会话测试: npx playwright test tests/specs/session.spec.js');
console.log('• 运行网络测试: npx playwright test tests/specs/network.spec.js');
console.log('• 生成HTML报告: npx playwright show-report');

console.log('\n🎯 测试覆盖范围:');
console.log('• 基础功能: 应用启动、界面加载、新建会话');
console.log('• 聊天功能: 消息发送、AI响应、流式显示');
console.log('• 会话管理: 会话历史、会话切换、数据持久化');
console.log('• 网络异常: fallback机制、错误处理、状态指示');

console.log('\n🔍 验证要点:');
console.log('• 聊天界面加载正常');
console.log('• 消息发送和接收功能');
console.log('• 会话历史保存和加载');
console.log('• 网络异常时的降级处理');
console.log('• 用户体验流畅性');
