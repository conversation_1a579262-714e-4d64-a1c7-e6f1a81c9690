/**
 * 测试选择器定义
 * 集中管理所有测试用的选择器
 */

const SELECTORS = {
  // 应用主界面
  app: {
    container: '[data-testid="app-container"]',
    loading: '[data-testid="app-loading"]',
    error: '[data-testid="app-error"]'
  },

  // 侧边栏
  sidebar: {
    container: '[data-testid="sidebar"]',
    newSessionButton: '[data-testid="new-session-btn"]',
    sessionList: '[data-testid="session-list"]',
    sessionItem: '[data-testid="session-item"]',
    settingsButton: '[data-testid="settings-btn"]'
  },

  // 聊天界面
  chat: {
    container: '[data-testid="chat-container"]',
    messageList: '[data-testid="message-list"]',
    userMessage: '[data-testid="user-message"]',
    aiMessage: '[data-testid="ai-message"]',
    inputArea: '[data-testid="chat-input-area"]',
    inputField: '[data-testid="chat-input"]',
    sendButton: '[data-testid="send-btn"]',
    typingIndicator: '[data-testid="typing-indicator"]'
  },

  // 消息相关
  message: {
    container: '[data-testid="message"]',
    content: '[data-testid="message-content"]',
    timestamp: '[data-testid="message-timestamp"]',
    avatar: '[data-testid="message-avatar"]'
  },

  // 状态指示器
  status: {
    networkStatus: '[data-testid="network-status"]',
    apiStatus: '[data-testid="api-status"]',
    connectionError: '[data-testid="connection-error"]',
    fallbackMode: '[data-testid="fallback-mode"]'
  },

  // 设置界面
  settings: {
    modal: '[data-testid="settings-modal"]',
    apiKeyInput: '[data-testid="api-key-input"]',
    saveButton: '[data-testid="settings-save-btn"]',
    cancelButton: '[data-testid="settings-cancel-btn"]'
  },

  // 错误提示
  error: {
    toast: '[data-testid="error-toast"]',
    message: '[data-testid="error-message"]',
    retryButton: '[data-testid="retry-btn"]'
  },

  // 加载状态
  loading: {
    spinner: '[data-testid="loading-spinner"]',
    overlay: '[data-testid="loading-overlay"]',
    text: '[data-testid="loading-text"]'
  }
};

// CSS选择器（备用）
const CSS_SELECTORS = {
  // Ant Design组件选择器
  antd: {
    button: '.ant-btn',
    input: '.ant-input',
    modal: '.ant-modal',
    message: '.ant-message',
    spin: '.ant-spin'
  },

  // 通用选择器
  common: {
    button: 'button',
    input: 'input[type="text"], textarea',
    form: 'form',
    list: 'ul, ol',
    listItem: 'li'
  }
};

// 文本内容选择器
const TEXT_SELECTORS = {
  buttons: {
    newSession: '新建会话',
    send: '发送',
    settings: '设置',
    save: '保存',
    cancel: '取消',
    retry: '重试'
  },

  messages: {
    welcome: '欢迎使用桌面AI助手',
    networkError: '网络连接异常',
    apiError: 'API调用失败',
    fallbackMode: '已切换到模拟模式'
  },

  placeholders: {
    chatInput: '请输入您的问题...',
    apiKey: '请输入API密钥'
  }
};

// 组合选择器函数
const COMBINED_SELECTORS = {
  /**
   * 获取特定会话项
   */
  sessionItem: (sessionId) => `[data-testid="session-item"][data-session-id="${sessionId}"]`,

  /**
   * 获取特定消息
   */
  messageByIndex: (index) => `[data-testid="message"]:nth-child(${index + 1})`,

  /**
   * 获取包含特定文本的元素
   */
  elementWithText: (text) => `//*[contains(text(), "${text}")]`,

  /**
   * 获取特定类型的消息
   */
  messageByType: (type) => `[data-testid="${type}-message"]`
};

module.exports = {
  SELECTORS,
  CSS_SELECTORS,
  TEXT_SELECTORS,
  COMBINED_SELECTORS
};
