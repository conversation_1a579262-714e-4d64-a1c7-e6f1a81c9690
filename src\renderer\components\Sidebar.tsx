import React, { useState } from 'react';
import { Button, List, Typography, Space, Dropdown, Modal } from 'antd';
import { PlusOutlined, MessageOutlined, MoreOutlined, DeleteOutlined, EditOutlined, SettingOutlined } from '@ant-design/icons';
import { useAppStore } from '../store/useAppStore';
import { useSessionService } from '../hooks/useSessionService';
import SettingsModal from './SettingsModal';

const { Text } = Typography;

const Sidebar: React.FC = () => {
  const {
    sessions,
    currentSessionId,
    setCurrentSession
  } = useAppStore();

  const {
    createNewSession,
    deleteSession,
    renameSession
  } = useSessionService();

  const [settingsVisible, setSettingsVisible] = useState(false);

  const sessionList = Object.values(sessions).sort((a, b) => b.updatedAt - a.updatedAt);

  const handleNewSession = () => {
    createNewSession();
  };

  const handleSelectSession = (sessionId: string) => {
    setCurrentSession(sessionId);
  };

  const handleDeleteSession = (sessionId: string) => {
    Modal.confirm({
      title: '删除会话',
      content: '确定要删除这个会话吗？此操作无法撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        deleteSession(sessionId);
      },
    });
  };

  const handleRenameSession = (sessionId: string, currentTitle: string) => {
    Modal.confirm({
      title: '重命名会话',
      content: (
        <input
          id="session-title-input"
          defaultValue={currentTitle}
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            marginTop: '8px'
          }}
        />
      ),
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const input = document.getElementById('session-title-input') as HTMLInputElement;
        const newTitle = input?.value.trim();
        if (newTitle && newTitle !== currentTitle) {
          renameSession(sessionId, newTitle);
        }
      },
    });
  };

  const getSessionMenuItems = (sessionId: string, title: string) => [
    {
      key: 'rename',
      label: '重命名',
      icon: <EditOutlined />,
      onClick: () => handleRenameSession(sessionId, title),
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: () => handleDeleteSession(sessionId),
    },
  ];

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString('zh-CN', { 
        weekday: 'short',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 新建会话按钮 */}
      <div style={{ padding: '16px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleNewSession}
          block
          size="large"
          data-testid="new-session-btn"
        >
          新建会话
        </Button>

        {/* 设置按钮 */}
        <Button
          icon={<SettingOutlined />}
          onClick={() => setSettingsVisible(true)}
          block
          style={{ marginTop: '8px' }}
          data-testid="settings-btn"
        >
          设置
        </Button>
      </div>

      {/* 会话列表 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        <List
          dataSource={sessionList}
          data-testid="session-list"
          renderItem={(session) => (
            <List.Item
              style={{
                padding: 0,
                border: 'none'
              }}
            >
              <div
                className={`session-item ${session.id === currentSessionId ? 'active' : ''}`}
                onClick={() => handleSelectSession(session.id)}
                style={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                data-testid="session-item"
                data-session-id={session.id}
              >
                <div style={{ flex: 1, minWidth: 0 }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                    <MessageOutlined style={{ marginRight: '8px', color: '#666' }} />
                    <Text 
                      strong 
                      ellipsis 
                      style={{ 
                        fontSize: '14px',
                        color: session.id === currentSessionId ? '#1677ff' : '#333'
                      }}
                    >
                      {session.title}
                    </Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {session.messageCount} 条消息
                    </Text>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {formatTime(session.updatedAt)}
                    </Text>
                  </div>
                </div>
                
                <Dropdown
                  menu={{ items: getSessionMenuItems(session.id, session.title) }}
                  trigger={['click']}
                  placement="bottomRight"
                >
                  <Button
                    type="text"
                    icon={<MoreOutlined />}
                    size="small"
                    onClick={(e) => e.stopPropagation()}
                    style={{ marginLeft: '8px' }}
                  />
                </Dropdown>
              </div>
            </List.Item>
          )}
        />
      </div>

      {/* 设置模态框 */}
      <SettingsModal
        visible={settingsVisible}
        onClose={() => setSettingsVisible(false)}
      />
    </div>
  );
};

export default Sidebar;
