const { app, Tray, nativeImage } = require('electron');
const path = require('path');
const fs = require('fs');

console.log('🔍 测试托盘图标创建...');

app.whenReady().then(() => {
  console.log('✅ Electron应用已准备就绪');
  
  // 测试图标路径
  const iconPath = path.join(__dirname, 'assets/icons');
  console.log('📁 图标目录:', iconPath);
  console.log('📁 目录是否存在:', fs.existsSync(iconPath));
  
  if (fs.existsSync(iconPath)) {
    const files = fs.readdirSync(iconPath);
    console.log('📄 图标文件列表:', files);
  }
  
  // 尝试不同的图标文件
  const iconFiles = [
    path.join(iconPath, 'tray.ico'),
    path.join(iconPath, 'tray.png'),
    path.join(iconPath, 'icon.ico'),
    path.join(iconPath, 'icon.png')
  ];
  
  let workingIcon = null;
  
  for (const iconFile of iconFiles) {
    console.log(`🔍 测试图标: ${iconFile}`);
    console.log(`📄 文件存在: ${fs.existsSync(iconFile)}`);
    
    if (fs.existsSync(iconFile)) {
      try {
        const image = nativeImage.createFromPath(iconFile);
        console.log(`📏 图标尺寸: ${image.getSize().width}x${image.getSize().height}`);
        console.log(`❓ 图标为空: ${image.isEmpty()}`);
        
        if (!image.isEmpty()) {
          workingIcon = iconFile;
          break;
        }
      } catch (error) {
        console.error(`❌ 加载图标失败: ${error.message}`);
      }
    }
  }
  
  if (workingIcon) {
    console.log(`✅ 使用图标: ${workingIcon}`);
    try {
      const image = nativeImage.createFromPath(workingIcon);
      const tray = new Tray(image);
      tray.setToolTip('测试托盘');
      console.log('✅ 托盘创建成功！');
      console.log('👀 请检查系统托盘区域');
      
      // 5秒后退出
      setTimeout(() => {
        console.log('🔚 测试结束，退出应用');
        app.quit();
      }, 5000);
      
    } catch (error) {
      console.error('❌ 托盘创建失败:', error);
      app.quit();
    }
  } else {
    console.error('❌ 没有找到可用的图标文件');
    app.quit();
  }
});

app.on('window-all-closed', () => {
  // 不退出，保持托盘
});
