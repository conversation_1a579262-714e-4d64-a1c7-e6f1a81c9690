{"appId": "com.desktop-ai-assistant.app", "productName": "桌面AI助手", "copyright": "Copyright © 2024 Desktop AI Assistant", "directories": {"output": "release", "buildResources": "build"}, "files": ["dist/**/*", "assets/**/*", "package.json", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "docs/", "to": "docs/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "assets/icons/icon.ico", "requestedExecutionLevel": "asInvoker", "publisherName": "Desktop AI Assistant", "verifyUpdateCodeSignature": false, "sign": null, "certificateFile": null, "certificatePassword": null}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "installerIcon": "assets/icons/icon.ico", "uninstallerIcon": "assets/icons/icon.ico", "installerHeaderIcon": "assets/icons/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "桌面AI助手", "include": "build/installer.nsh", "script": "build/installer.nsh", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Productivity"}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "publish": null, "buildDependenciesFromSource": false, "nodeGypRebuild": false, "npmRebuild": true, "buildVersion": "1.0.0"}