// 测试通义千问API连接
const axios = require('axios');
const fs = require('fs');

// 读取API密钥
const envContent = fs.readFileSync('./docs/env', 'utf8');
const apiKey = envContent.split('=')[1].trim();

console.log('🔑 API密钥:', apiKey.substring(0, 10) + '...');

// API配置
const API_CONFIG = {
  BASE_URL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  MODEL: 'qwen-plus',
  TEMPERATURE: 0.7,
  MAX_TOKENS: 2000,
};

// 测试API连接
async function testAPI() {
  try {
    console.log('🚀 开始测试API连接...');
    
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}/chat/completions`,
      {
        model: API_CONFIG.MODEL,
        messages: [
          {
            role: 'system',
            content: '你是一个有用的AI助手，请用中文回答用户的问题。'
          },
          {
            role: 'user',
            content: '你好，请简单介绍一下自己'
          }
        ],
        temperature: API_CONFIG.TEMPERATURE,
        max_tokens: API_CONFIG.MAX_TOKENS,
        stream: false // 使用非流式响应进行测试
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );

    console.log('✅ API调用成功！');
    console.log('📝 AI响应:', response.data.choices[0].message.content);
    console.log('📊 使用的模型:', response.data.model);
    console.log('🔢 Token使用情况:', response.data.usage);
    
  } catch (error) {
    console.error('❌ API调用失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else if (error.request) {
      console.error('网络错误:', error.message);
    } else {
      console.error('其他错误:', error.message);
    }
  }
}

// 运行测试
testAPI();
