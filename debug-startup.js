// 调试启动问题的脚本
console.log('🔍 开始调试启动问题...');

// 检查Node.js版本
console.log('📋 Node.js版本:', process.version);
console.log('📋 平台:', process.platform);
console.log('📋 架构:', process.arch);

// 检查必要的模块是否可以加载
const modules = [
  'electron',
  'path',
  'fs',
  'axios'
];

console.log('\n📦 检查模块加载...');
for (const moduleName of modules) {
  try {
    require(moduleName);
    console.log(`✅ ${moduleName} - 加载成功`);
  } catch (error) {
    console.log(`❌ ${moduleName} - 加载失败:`, error.message);
  }
}

// 检查构建文件
const fs = require('fs');
const path = require('path');

console.log('\n📁 检查构建文件...');
const buildFiles = [
  'dist/main/main.js',
  'dist/main/preload.js',
  'dist/renderer/index.html',
  'dist/renderer/renderer.js'
];

for (const file of buildFiles) {
  try {
    const stats = fs.statSync(file);
    console.log(`✅ ${file} - 存在 (${Math.round(stats.size / 1024)}KB)`);
  } catch (error) {
    console.log(`❌ ${file} - 不存在`);
  }
}

// 尝试加载主进程文件
console.log('\n🔧 尝试加载主进程...');
try {
  // 设置环境变量
  process.env.NODE_ENV = 'development';
  
  console.log('📝 当前工作目录:', process.cwd());
  console.log('📝 主文件路径:', path.resolve('dist/main/main.js'));
  
  // 检查文件是否可读
  const mainFile = path.resolve('dist/main/main.js');
  fs.accessSync(mainFile, fs.constants.R_OK);
  console.log('✅ 主文件可读');
  
  // 尝试读取文件开头
  const content = fs.readFileSync(mainFile, 'utf8');
  console.log('✅ 主文件可以读取，大小:', Math.round(content.length / 1024), 'KB');
  
  // 检查是否包含关键代码
  if (content.includes('Application')) {
    console.log('✅ 主文件包含Application类');
  } else {
    console.log('❌ 主文件不包含Application类');
  }
  
} catch (error) {
  console.log('❌ 加载主进程失败:', error.message);
  console.log('错误详情:', error);
}

// 检查Electron是否可以启动
console.log('\n⚡ 检查Electron...');
try {
  const { app } = require('electron');
  console.log('✅ Electron模块加载成功');
  console.log('📝 Electron版本:', process.versions.electron);
} catch (error) {
  console.log('❌ Electron模块加载失败:', error.message);
}

// 检查环境变量
console.log('\n🌍 环境变量检查...');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('ELECTRON_IS_DEV:', process.env.ELECTRON_IS_DEV);

console.log('\n🎯 调试完成！');
console.log('\n💡 建议：');
console.log('1. 如果所有检查都通过，问题可能是Electron窗口创建相关');
console.log('2. 如果有模块加载失败，需要重新安装依赖');
console.log('3. 如果主文件有问题，需要重新构建');
