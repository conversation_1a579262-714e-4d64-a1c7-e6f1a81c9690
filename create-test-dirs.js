const fs = require('fs');
const path = require('path');

const dirs = [
  'tests',
  'tests/setup',
  'tests/specs', 
  'tests/utils',
  'tests/reports'
];

dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  } else {
    console.log(`Directory already exists: ${dir}`);
  }
});

console.log('Test directory structure created successfully!');
