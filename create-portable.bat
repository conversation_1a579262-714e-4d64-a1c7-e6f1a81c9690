@echo off
echo 🚀 创建便携版桌面AI助手
echo.

echo 📋 检查构建文件...
if not exist "dist\main\main.js" (
    echo ❌ 构建文件不存在，正在构建...
    call npm run build
    if %errorlevel% neq 0 (
        echo ❌ 构建失败
        pause
        exit /b 1
    )
)

echo ✅ 构建文件存在
echo.

echo 📦 创建便携版目录...
if exist "portable" rmdir /s /q portable
mkdir portable
mkdir portable\dist
mkdir portable\assets
mkdir portable\node_modules

echo 📋 复制文件...
xcopy /s /e /y dist portable\dist\
xcopy /s /e /y assets portable\assets\
copy package.json portable\
copy README.md portable\

echo 📦 安装生产依赖...
cd portable
call npm install --production --no-optional
cd ..

echo 🎯 创建启动脚本...
echo @echo off > portable\start.bat
echo echo 🚀 启动桌面AI助手... >> portable\start.bat
echo echo 应用将启动到系统托盘 >> portable\start.bat
echo node_modules\.bin\electron . >> portable\start.bat
echo pause >> portable\start.bat

echo ✅ 便携版创建完成！
echo 📁 位置: portable\
echo 🚀 运行: portable\start.bat
echo.
pause
