# 桌面AI助手重新构建指南

## 🎯 为什么需要重新构建？

由于我们对系统托盘功能进行了以下重要修改，需要重新构建应用程序才能使新功能生效：

### 修改的文件
- `src/main/main.ts` - 主应用逻辑
- `src/main/tray.ts` - 托盘管理器
- `src/main/window.ts` - 窗口管理器
- `src/shared/types.ts` - 类型定义
- `src/renderer/components/SettingsModal.tsx` - 设置界面

### 新增的功能
- 启动时隐藏到系统托盘
- 托盘菜单完整功能
- 配置选项：启动时是否显示窗口
- 增强的窗口管理

## 🔧 构建步骤

### 方法1：完整构建（推荐）
```bash
# 1. 停止当前运行的应用
# 按 Ctrl+C 或关闭终端

# 2. 清理之前的构建文件
npm run clean

# 3. 重新构建
npm run build

# 4. 启动应用测试
npm start
```

### 方法2：分步构建
```bash
# 1. 构建渲染进程（React前端）
npm run build:renderer

# 2. 构建主进程（Electron后端）
npm run build:main

# 3. 启动应用
npm start
```

### 方法3：开发模式（实时编译）
```bash
# 启动开发模式（自动重新编译）
npm run dev
```

## 📦 打包为可执行文件

如果您想创建独立的exe文件：

```bash
# 打包为可执行文件（不安装）
npm run pack

# 创建安装包
npm run dist:win
```

## 🔍 验证构建结果

### 检查构建文件
构建完成后，应该看到以下文件：
- `dist/main/main.js` - 主进程文件
- `dist/renderer/index.html` - 渲染进程文件
- `dist/renderer/renderer.js` - 前端JavaScript

### 测试新功能
1. **启动测试**: 运行 `npm start`，应用应该隐藏到系统托盘
2. **托盘图标**: 检查系统托盘（右下角）是否出现应用图标
3. **托盘点击**: 单击托盘图标应该显示/隐藏窗口
4. **托盘菜单**: 右键托盘图标应该显示完整菜单
5. **设置选项**: 打开设置，应该看到"启动时显示窗口"选项

## 🚨 常见问题

### 构建卡住或失败
```bash
# 清理node_modules和重新安装
rm -rf node_modules package-lock.json
npm install

# 或者在Windows中
rmdir /s node_modules
del package-lock.json
npm install
```

### TypeScript编译错误
```bash
# 检查TypeScript配置
npx tsc --noEmit

# 如果有类型错误，检查以下文件：
# - src/shared/types.ts
# - src/main/tray.ts
# - src/main/main.ts
```

### Webpack构建问题
```bash
# 检查webpack配置
npx webpack --config webpack.main.config.js --mode development
npx webpack --config webpack.renderer.config.js --mode development
```

## 📋 构建检查清单

构建完成后，请验证：

- [ ] `dist/main/main.js` 文件存在且包含新的托盘代码
- [ ] `dist/renderer/` 目录包含更新的前端文件
- [ ] 应用启动时隐藏到托盘
- [ ] 托盘图标正确显示
- [ ] 托盘菜单功能正常
- [ ] 设置中的新选项可用
- [ ] 窗口关闭时隐藏而不退出

## 🎉 完成

构建完成后，您的桌面AI助手将具备完整的系统托盘功能！

### 新功能使用提示：
1. **首次启动**: 应用将隐藏到托盘，查看右下角托盘图标
2. **快速访问**: 单击托盘图标显示/隐藏窗口
3. **右键菜单**: 右键托盘图标使用快捷功能
4. **自定义启动**: 在设置中调整启动行为
5. **完全退出**: 通过托盘菜单选择"退出"

享受更好的用户体验！🚀
