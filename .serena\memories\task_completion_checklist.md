# 任务完成检查清单

## 开发任务完成后应该执行的步骤

### 1. 代码质量检查
```bash
# TypeScript类型检查
npx tsc --noEmit

# ESLint代码检查（如果配置了）
npx eslint src/ --ext .ts,.tsx --fix
```

### 2. 构建验证
```bash
# 清理旧的构建文件
npm run clean

# 重新构建
npm run build

# 验证构建输出
# 检查 dist/ 目录是否正确生成
```

### 3. 功能测试
```bash
# 启动应用进行手动测试
npm start

# 检查以下功能：
# - 应用正常启动
# - UI界面正常显示
# - AI对话功能正常
# - 系统托盘功能正常
# - 快捷键功能正常
# - 设置保存功能正常
```

### 4. 打包测试
```bash
# 创建安装包
npm run dist:win

# 验证安装包
# - 检查 release/ 目录
# - 安装包大小合理
# - 安装程序能正常运行
```

### 5. 文档更新
- 更新README.md（如有必要）
- 更新版本号（package.json）
- 更新CHANGELOG（如有）

### 6. Git提交
```bash
# 检查状态
git status

# 添加更改
git add .

# 提交更改
git commit -m "feat: 描述你的更改"

# 推送到远程（如果需要）
git push
```

## 发布前的最终检查
1. ✅ 所有功能正常工作
2. ✅ 没有TypeScript错误
3. ✅ 没有ESLint警告
4. ✅ 构建成功
5. ✅ 打包成功
6. ✅ 安装程序测试通过
7. ✅ 文档更新完成
8. ✅ 版本号正确
9. ✅ Git提交完成

## 常见问题排查
- **构建失败**: 检查TypeScript错误和依赖问题
- **应用无法启动**: 检查主进程代码和preload脚本
- **功能异常**: 检查IPC通信和API配置
- **打包失败**: 检查electron-builder配置和资源文件