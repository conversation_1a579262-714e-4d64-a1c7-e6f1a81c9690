/**
 * 快速测试脚本
 * 验证Playwright和Electron集成是否正常工作
 */

const { _electron: electron } = require('playwright');
const path = require('path');

async function quickTest() {
  console.log('🚀 开始快速测试...');
  
  let electronApp;
  let page;
  
  try {
    console.log('📱 启动Electron应用...');
    
    // 启动Electron应用
    electronApp = await electron.launch({
      args: [path.join(__dirname, 'dist/main/main.js')],
      env: {
        NODE_ENV: 'test',
        ELECTRON_IS_DEV: '1'
      }
    });

    console.log('✅ Electron应用启动成功');

    // 获取第一个窗口
    page = await electronApp.firstWindow();
    console.log('✅ 获取应用窗口成功');

    // 等待页面加载
    await page.waitForLoadState('domcontentloaded');
    console.log('✅ 页面加载完成');

    // 检查页面标题
    const title = await page.title();
    console.log(`📋 页面标题: ${title}`);

    // 检查应用容器是否存在
    const appContainer = page.locator('[data-testid="app-container"]');
    if (await appContainer.count() > 0) {
      console.log('✅ 应用容器元素存在');
    } else {
      console.log('⚠️ 应用容器元素不存在，检查data-testid属性');
    }

    // 检查侧边栏是否存在
    const sidebar = page.locator('[data-testid="sidebar"]');
    if (await sidebar.count() > 0) {
      console.log('✅ 侧边栏元素存在');
    } else {
      console.log('⚠️ 侧边栏元素不存在');
    }

    // 检查新建会话按钮
    const newSessionBtn = page.locator('[data-testid="new-session-btn"]');
    if (await newSessionBtn.count() > 0) {
      console.log('✅ 新建会话按钮存在');
      
      // 尝试点击新建会话按钮
      await newSessionBtn.click();
      console.log('✅ 成功点击新建会话按钮');
      
      // 等待一下
      await page.waitForTimeout(1000);
      
      // 检查聊天输入区域
      const chatInput = page.locator('[data-testid="chat-input"]');
      if (await chatInput.count() > 0) {
        console.log('✅ 聊天输入框存在');
        
        // 尝试输入测试消息
        await chatInput.fill('这是一条测试消息');
        console.log('✅ 成功输入测试消息');
        
        // 检查发送按钮
        const sendBtn = page.locator('[data-testid="send-btn"]');
        if (await sendBtn.count() > 0) {
          console.log('✅ 发送按钮存在');
        } else {
          console.log('⚠️ 发送按钮不存在');
        }
      } else {
        console.log('⚠️ 聊天输入框不存在');
      }
    } else {
      console.log('⚠️ 新建会话按钮不存在');
    }

    // 截图
    await page.screenshot({ path: 'test-results/quick-test-screenshot.png' });
    console.log('📸 已保存截图: test-results/quick-test-screenshot.png');

    console.log('\n🎉 快速测试完成！');
    console.log('✅ Playwright + Electron 集成正常工作');
    console.log('✅ 应用界面元素可以正常访问');
    console.log('✅ 测试标识符(data-testid)配置正确');

  } catch (error) {
    console.error('❌ 快速测试失败:', error.message);
    console.error(error.stack);
  } finally {
    // 清理
    if (electronApp) {
      console.log('🧹 关闭Electron应用...');
      await electronApp.close();
    }
  }
}

// 运行快速测试
quickTest().catch(console.error);
