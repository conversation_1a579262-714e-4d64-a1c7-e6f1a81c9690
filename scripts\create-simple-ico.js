// 创建简单的256x256 ICO文件
const fs = require('fs');
const path = require('path');

// 确保图标目录存在
const iconsDir = path.join(__dirname, '../assets/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// 创建一个简单的256x256 ICO文件
function createSimple256ICO() {
  // ICO文件头 (6 bytes)
  const header = Buffer.alloc(6);
  header.writeUInt16LE(0, 0);      // Reserved (0)
  header.writeUInt16LE(1, 2);      // Type (1 = ICO)
  header.writeUInt16LE(1, 4);      // Number of images (1)

  // 图像目录条目 (16 bytes)
  const dirEntry = Buffer.alloc(16);
  dirEntry.writeUInt8(0, 0);       // Width (0 = 256)
  dirEntry.writeUInt8(0, 1);       // Height (0 = 256)
  dirEntry.writeUInt8(0, 2);       // Color count (0)
  dirEntry.writeUInt8(0, 3);       // Reserved (0)
  dirEntry.writeUInt16LE(1, 4);    // Color planes (1)
  dirEntry.writeUInt16LE(32, 6);   // Bits per pixel (32)
  
  const imageSize = 256 * 256 * 4 + 40; // RGBA data + BITMAPINFOHEADER
  dirEntry.writeUInt32LE(imageSize, 8);  // Size of image data
  dirEntry.writeUInt32LE(22, 12);        // Offset to image data

  // BITMAPINFOHEADER (40 bytes)
  const bmpHeader = Buffer.alloc(40);
  bmpHeader.writeUInt32LE(40, 0);        // Header size
  bmpHeader.writeInt32LE(256, 4);        // Width
  bmpHeader.writeInt32LE(512, 8);        // Height (doubled for ICO)
  bmpHeader.writeUInt16LE(1, 12);        // Planes
  bmpHeader.writeUInt16LE(32, 14);       // Bits per pixel
  bmpHeader.writeUInt32LE(0, 16);        // Compression
  bmpHeader.writeUInt32LE(256 * 256 * 4, 20); // Image size
  bmpHeader.writeUInt32LE(0, 24);        // X pixels per meter
  bmpHeader.writeUInt32LE(0, 28);        // Y pixels per meter
  bmpHeader.writeUInt32LE(0, 32);        // Colors used
  bmpHeader.writeUInt32LE(0, 36);        // Important colors

  // 创建256x256的RGBA像素数据
  const pixelData = Buffer.alloc(256 * 256 * 4);
  
  // 填充蓝色背景 (#1677ff)
  for (let y = 0; y < 256; y++) {
    for (let x = 0; x < 256; x++) {
      const offset = ((255 - y) * 256 + x) * 4; // ICO uses bottom-up format
      pixelData[offset] = 0xff;     // Blue
      pixelData[offset + 1] = 0x77; // Green
      pixelData[offset + 2] = 0x16; // Red
      pixelData[offset + 3] = 0xff; // Alpha
    }
  }

  // 创建简单的"AI"文字效果
  const centerX = 128;
  const centerY = 128;
  const fontSize = 80;
  
  // 简单的像素字体 "A"
  for (let y = centerY - fontSize/2; y < centerY + fontSize/2; y++) {
    for (let x = centerX - fontSize; x < centerX - fontSize/4; x++) {
      if (x >= 0 && x < 256 && y >= 0 && y < 256) {
        const offset = ((255 - y) * 256 + x) * 4;
        // 创建"A"的左边
        if ((y - centerY + fontSize/2) / fontSize > (x - centerX + fontSize) / (fontSize * 0.75) && 
            y > centerY - fontSize/4) {
          pixelData[offset] = 0xff;     // White
          pixelData[offset + 1] = 0xff;
          pixelData[offset + 2] = 0xff;
          pixelData[offset + 3] = 0xff;
        }
      }
    }
  }
  
  // 简单的像素字体 "I"
  for (let y = centerY - fontSize/2; y < centerY + fontSize/2; y++) {
    for (let x = centerX + fontSize/4; x < centerX + fontSize; x++) {
      if (x >= 0 && x < 256 && y >= 0 && y < 256) {
        const offset = ((255 - y) * 256 + x) * 4;
        // 创建"I"
        if (x > centerX + fontSize/2 - 10 && x < centerX + fontSize/2 + 10) {
          pixelData[offset] = 0xff;     // White
          pixelData[offset + 1] = 0xff;
          pixelData[offset + 2] = 0xff;
          pixelData[offset + 3] = 0xff;
        }
      }
    }
  }

  // AND mask (全透明)
  const andMask = Buffer.alloc(256 * 256 / 8);
  andMask.fill(0);

  // 组合所有部分
  return Buffer.concat([header, dirEntry, bmpHeader, pixelData, andMask]);
}

// 创建ICO文件
const icoBuffer = createSimple256ICO();
const icoPath = path.join(iconsDir, 'icon.ico');
fs.writeFileSync(icoPath, icoBuffer);

console.log(`Created 256x256 ICO file: ${icoPath}`);
console.log('ICO文件创建完成！');
