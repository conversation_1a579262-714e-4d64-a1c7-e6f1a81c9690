import React, { useEffect, useRef } from 'react';
import { Avatar, Typography, Spin } from 'antd';
import { UserOutlined, RobotOutlined, LoadingOutlined } from '@ant-design/icons';
import { Message } from '@shared/types';
import { useAppStore } from '../store/useAppStore';

const { Text } = Typography;

interface MessageListProps {
  messages: Message[];
  sessionId: string;
}

const MessageList: React.FC<MessageListProps> = ({ messages, sessionId }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isLoading } = useAppStore();

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderMessage = (message: Message, index: number) => {
    const isUser = message.role === 'user';
    const isLastMessage = index === messages.length - 1;

    return (
      <div
        key={message.id}
        style={{
          display: 'flex',
          flexDirection: isUser ? 'row-reverse' : 'row',
          alignItems: 'flex-start',
          marginBottom: '16px',
          padding: '0 24px'
        }}
        data-testid={isUser ? 'user-message' : 'ai-message'}
        data-message-id={message.id}
      >
        {/* 头像 */}
        <Avatar
          size={32}
          icon={isUser ? <UserOutlined /> : <RobotOutlined />}
          style={{
            backgroundColor: isUser ? '#1677ff' : '#52c41a',
            flexShrink: 0,
            margin: isUser ? '0 0 0 12px' : '0 12px 0 0'
          }}
        />

        {/* 消息内容 */}
        <div
          style={{
            maxWidth: '70%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: isUser ? 'flex-end' : 'flex-start'
          }}
        >
          {/* 消息气泡 */}
          <div
            className={`message-bubble ${isUser ? 'user' : 'assistant'}`}
            style={{
              position: 'relative'
            }}
          >
            <div
              style={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                lineHeight: '1.5'
              }}
              data-testid="message-content"
            >
              {message.content}
            </div>
          </div>

          {/* 时间戳 */}
          <Text
            type="secondary"
            style={{
              fontSize: '11px',
              marginTop: '4px',
              textAlign: isUser ? 'right' : 'left'
            }}
          >
            {formatTime(message.timestamp)}
          </Text>
        </div>
      </div>
    );
  };

  // 渲染AI思考中的指示器
  const renderTypingIndicator = () => {
    if (!isLoading) return null;

    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'flex-start',
          marginBottom: '16px',
          padding: '0 24px'
        }}
        data-testid="typing-indicator"
      >
        {/* AI头像 */}
        <Avatar
          size={32}
          icon={<RobotOutlined />}
          style={{
            backgroundColor: '#52c41a',
            flexShrink: 0,
            margin: '0 12px 0 0'
          }}
        />

        {/* 思考中的内容 */}
        <div
          style={{
            maxWidth: '70%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start'
          }}
        >
          {/* 思考中的气泡 */}
          <div
            className="message-bubble assistant"
            style={{
              position: 'relative',
              padding: '12px 16px',
              backgroundColor: '#f0f0f0',
              borderRadius: '18px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <Spin
              indicator={<LoadingOutlined style={{ fontSize: 14 }} spin />}
              size="small"
            />
            <span style={{ color: '#666', fontSize: '14px' }}>
              AI正在思考中...
            </span>
          </div>
        </div>
      </div>
    );
  };

  if (messages.length === 0) {
    return (
      <div
        style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999',
          fontSize: '14px'
        }}
      >
        开始你的第一条消息吧...
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      style={{
        height: '100%',
        overflowY: 'auto',
        padding: '16px 0'
      }}
      data-testid="message-list"
    >
      {messages.map((message, index) => renderMessage(message, index))}

      {/* AI思考中指示器 */}
      {renderTypingIndicator()}

      {/* 滚动锚点 */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
