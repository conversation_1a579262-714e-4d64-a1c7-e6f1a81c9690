const { test, expect } = require('@playwright/test');
const { electron } = require('playwright');
const path = require('path');

test.describe('系统托盘功能测试', () => {
  let electronApp;
  let page;

  test.beforeAll(async () => {
    // 启动Electron应用
    electronApp = await electron.launch({
      args: [path.join(__dirname, '../../dist/main/main.js')],
      env: {
        NODE_ENV: 'test',
        ELECTRON_IS_DEV: '1'
      }
    });

    // 获取第一个页面
    page = await electronApp.firstWindow();
    
    // 等待页面加载
    await page.waitForLoadState('domcontentloaded');
  });

  test.afterAll(async () => {
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('应用启动时应该隐藏主窗口', async () => {
    // 等待一段时间确保应用完全启动
    await page.waitForTimeout(2000);
    
    // 检查窗口是否隐藏（在测试环境中可能仍然可见）
    // 这个测试主要验证应用能正常启动
    const isVisible = await page.isVisible('body');
    console.log('页面是否可见:', isVisible);
    
    // 验证应用基本结构
    await expect(page.locator('[data-testid="app-container"]')).toBeVisible();
  });

  test('应该能够通过IPC显示和隐藏窗口', async () => {
    // 测试窗口显示
    await page.evaluate(() => {
      return window.electronAPI.window.show();
    });
    
    await page.waitForTimeout(500);
    
    // 测试窗口隐藏
    await page.evaluate(() => {
      return window.electronAPI.window.hide();
    });
    
    await page.waitForTimeout(500);
  });

  test('设置中应该包含启动时显示窗口的选项', async () => {
    // 确保窗口可见
    await page.evaluate(() => {
      return window.electronAPI.window.show();
    });
    
    // 打开设置
    const settingsButton = page.locator('[data-testid="settings-button"]');
    await settingsButton.click();
    
    // 等待设置模态框出现
    await expect(page.locator('[data-testid="settings-modal"]')).toBeVisible();
    
    // 查找启动时显示窗口的选项
    const showOnStartupOption = page.locator('text=启动时显示窗口');
    await expect(showOnStartupOption).toBeVisible();
    
    // 关闭设置
    const cancelButton = page.locator('[data-testid="settings-cancel"]');
    await cancelButton.click();
  });

  test('应该能够创建新会话', async () => {
    // 确保窗口可见
    await page.evaluate(() => {
      return window.electronAPI.window.show();
    });
    
    // 点击新建会话按钮
    const newSessionButton = page.locator('[data-testid="new-session-button"]');
    await newSessionButton.click();
    
    // 验证新会话已创建
    await page.waitForTimeout(1000);
    
    // 检查是否有会话列表项
    const sessionItems = page.locator('[data-testid="session-item"]');
    const count = await sessionItems.count();
    expect(count).toBeGreaterThan(0);
  });

  test('应该能够正确处理窗口关闭事件', async () => {
    // 确保窗口可见
    await page.evaluate(() => {
      return window.electronAPI.window.show();
    });
    
    // 模拟窗口关闭
    await page.evaluate(() => {
      return window.electronAPI.window.close();
    });
    
    await page.waitForTimeout(500);
    
    // 验证应用仍在运行（页面仍然可访问）
    const isConnected = await page.evaluate(() => {
      return typeof window.electronAPI !== 'undefined';
    });
    
    expect(isConnected).toBe(true);
  });

  test('应该能够获取应用版本信息', async () => {
    const version = await page.evaluate(() => {
      return window.electronAPI.app.getVersion();
    });
    
    expect(version).toBeTruthy();
    expect(typeof version).toBe('string');
    console.log('应用版本:', version);
  });

  test('应该能够保存和读取配置', async () => {
    const testConfig = {
      apiKey: 'test-key',
      model: 'qwen-plus',
      temperature: 0.8,
      maxTokens: 1500,
      autoStart: true,
      showOnStartup: true
    };
    
    // 保存配置
    await page.evaluate((config) => {
      return window.electronAPI.store.set('test-config', config);
    }, testConfig);
    
    // 读取配置
    const savedConfig = await page.evaluate(() => {
      return window.electronAPI.store.get('test-config');
    });
    
    expect(savedConfig).toEqual(testConfig);
    
    // 清理测试数据
    await page.evaluate(() => {
      return window.electronAPI.store.delete('test-config');
    });
  });

  test('控制台不应该有严重错误', async () => {
    const errors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // 执行一些基本操作
    await page.waitForTimeout(2000);
    
    // 检查是否有严重错误
    const seriousErrors = errors.filter(error => 
      !error.includes('DevTools') && 
      !error.includes('favicon') &&
      !error.includes('Warning')
    );
    
    if (seriousErrors.length > 0) {
      console.log('发现错误:', seriousErrors);
    }
    
    expect(seriousErrors.length).toBe(0);
  });
});
