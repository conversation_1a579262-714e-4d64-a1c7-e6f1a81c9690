// 测试API连接脚本
const axios = require('axios');
const fs = require('fs');

console.log('🚀 开始测试通义千问API连接...');

// 读取API密钥
let apiKey = '';
try {
    const envContent = fs.readFileSync('./docs/env', 'utf8');
    apiKey = envContent.split('=')[1].trim();
    console.log('🔑 API密钥已读取:', apiKey.substring(0, 10) + '...');
} catch (error) {
    console.error('❌ 读取API密钥失败:', error.message);
    process.exit(1);
}

// API配置
const API_CONFIG = {
    BASE_URL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    MODEL: 'qwen-plus',
    TEMPERATURE: 0.7,
    MAX_TOKENS: 2000,
    TIMEOUT: 30000
};

console.log('📡 API配置:', {
    baseURL: API_CONFIG.BASE_URL,
    model: API_CONFIG.MODEL,
    timeout: API_CONFIG.TIMEOUT
});

// 创建axios实例
const apiClient = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
    }
});

// 测试消息
const testMessage = '你好，请简单介绍一下你自己';

async function testAPI() {
    try {
        console.log('\n📤 发送测试消息:', testMessage);
        
        const response = await apiClient.post('/chat/completions', {
            model: API_CONFIG.MODEL,
            messages: [
                {
                    role: 'system',
                    content: '你是一个有用的AI助手，请用中文回答用户的问题。'
                },
                {
                    role: 'user',
                    content: testMessage
                }
            ],
            stream: false,
            temperature: API_CONFIG.TEMPERATURE,
            max_tokens: API_CONFIG.MAX_TOKENS
        });

        console.log('✅ API调用成功!');
        console.log('📥 响应状态:', response.status);
        console.log('📥 响应数据:', JSON.stringify(response.data, null, 2));
        
        if (response.data.choices && response.data.choices[0]) {
            const aiResponse = response.data.choices[0].message.content;
            console.log('\n🤖 AI回复:', aiResponse);
        }

    } catch (error) {
        console.error('❌ API调用失败:');
        
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应头:', error.response.headers);
            console.error('响应数据:', error.response.data);
        } else if (error.request) {
            console.error('请求错误:', error.request);
        } else {
            console.error('错误信息:', error.message);
        }
        
        console.error('完整错误:', error);
    }
}

// 测试流式API
async function testStreamAPI() {
    try {
        console.log('\n🌊 测试流式API...');
        console.log('📤 发送流式测试消息:', testMessage);
        
        const response = await apiClient.post('/chat/completions', {
            model: API_CONFIG.MODEL,
            messages: [
                {
                    role: 'system',
                    content: '你是一个有用的AI助手，请用中文回答用户的问题。'
                },
                {
                    role: 'user',
                    content: testMessage
                }
            ],
            stream: true,
            temperature: API_CONFIG.TEMPERATURE,
            max_tokens: API_CONFIG.MAX_TOKENS
        }, {
            responseType: 'stream'
        });

        console.log('✅ 流式API连接成功!');
        console.log('📥 响应状态:', response.status);
        
        let fullMessage = '';
        
        response.data.on('data', (chunk) => {
            const lines = chunk.toString().split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6).trim();
                    
                    if (data === '[DONE]') {
                        console.log('\n🏁 流式响应完成');
                        console.log('🤖 完整AI回复:', fullMessage);
                        return;
                    }

                    try {
                        const parsed = JSON.parse(data);
                        const content = parsed.choices[0]?.delta?.content;
                        
                        if (content) {
                            process.stdout.write(content);
                            fullMessage += content;
                        }
                    } catch (parseError) {
                        // 忽略解析错误
                    }
                }
            }
        });

        response.data.on('error', (error) => {
            console.error('\n❌ 流式响应错误:', error);
        });

        response.data.on('end', () => {
            console.log('\n✅ 流式响应结束');
        });

    } catch (error) {
        console.error('❌ 流式API调用失败:');
        
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应头:', error.response.headers);
            console.error('响应数据:', error.response.data);
        } else if (error.request) {
            console.error('请求错误:', error.request);
        } else {
            console.error('错误信息:', error.message);
        }
    }
}

// 运行测试
async function runTests() {
    console.log('🧪 开始API测试...\n');
    
    // 测试普通API
    await testAPI();
    
    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试流式API
    await testStreamAPI();
    
    // 等待流式响应完成
    setTimeout(() => {
        console.log('\n🎉 测试完成!');
        process.exit(0);
    }, 5000);
}

runTests();
