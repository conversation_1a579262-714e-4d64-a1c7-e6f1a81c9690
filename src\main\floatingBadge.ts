import { BrowserWindow, screen, app } from 'electron';
import * as path from 'path';

export class FloatingBadgeManager {
  private badgeWindow: BrowserWindow | null = null;
  private onBadgeClick?: () => void;

  constructor() {
    // 构造函数
  }

  /**
   * 创建桌面浮标窗口
   */
  createFloatingBadge(onBadgeClick?: () => void): void {
    this.onBadgeClick = onBadgeClick;

    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    // 浮标尺寸
    const badgeWidth = 60;
    const badgeHeight = 60;
    
    // 浮标位置（右侧中间偏上）
    const x = width - badgeWidth - 20; // 距离右边缘20px
    const y = Math.floor(height * 0.3); // 屏幕高度的30%位置

    this.badgeWindow = new BrowserWindow({
      width: badgeWidth,
      height: badgeHeight,
      x: x,
      y: y,
      frame: false, // 无边框
      show: false, // 初始不显示
      resizable: false, // 不可调整大小
      maximizable: false, // 不可最大化
      minimizable: false, // 不可最小化
      closable: false, // 不可关闭
      alwaysOnTop: true, // 始终置顶
      skipTaskbar: true, // 不显示在任务栏
      transparent: true, // 透明背景
      hasShadow: false, // 无阴影
      focusable: false, // 不可聚焦（避免抢夺焦点）
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
    });

    // 加载浮标HTML内容
    this.loadBadgeContent();

    // 设置窗口事件
    this.setupBadgeEvents();

    // 显示浮标
    this.badgeWindow.once('ready-to-show', () => {
      if (this.badgeWindow) {
        this.badgeWindow.show();
        this.badgeWindow.setAlwaysOnTop(true, 'floating');
      }
    });
  }

  /**
   * 加载浮标内容
   */
  private loadBadgeContent(): void {
    if (!this.badgeWindow) return;

    // 创建精美的浮标HTML内容
    const badgeHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            width: 60px;
            height: 60px;
            background: transparent;
            overflow: hidden;
            cursor: pointer;
            user-select: none;
            -webkit-user-select: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
          }

          .badge-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #1677ff 0%, #4096ff 50%, #69b1ff 100%);
            border-radius: 50%;
            box-shadow:
              0 4px 12px rgba(22, 119, 255, 0.3),
              0 2px 6px rgba(22, 119, 255, 0.2),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid rgba(255, 255, 255, 0.15);
            position: relative;
            overflow: hidden;
          }

          .badge-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
          }

          .badge-container:hover {
            transform: scale(1.05) translateY(-2px);
            box-shadow:
              0 8px 20px rgba(22, 119, 255, 0.4),
              0 4px 12px rgba(22, 119, 255, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.4);
          }

          .badge-container:hover::before {
            opacity: 1;
            transform: rotate(45deg) translate(50%, 50%);
          }

          .badge-container:active {
            transform: scale(0.95);
            transition: all 0.1s ease;
          }

          .badge-icon {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            color: #1677ff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
          }

          .status-indicator {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #52c41a;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            z-index: 3;
          }

          .pulse {
            animation: pulse 3s infinite ease-in-out;
          }

          .status-indicator.thinking {
            background: #faad14;
            animation: thinking 1.5s infinite ease-in-out;
          }

          @keyframes pulse {
            0%, 100% {
              box-shadow:
                0 4px 12px rgba(22, 119, 255, 0.3),
                0 2px 6px rgba(22, 119, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            50% {
              box-shadow:
                0 6px 16px rgba(22, 119, 255, 0.5),
                0 3px 8px rgba(22, 119, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            }
          }

          @keyframes thinking {
            0%, 100% {
              transform: scale(1);
              opacity: 1;
            }
            50% {
              transform: scale(1.2);
              opacity: 0.8;
            }
          }

          .tooltip {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 10;
          }

          .badge-container:hover .tooltip {
            opacity: 1;
          }
        </style>
      </head>
      <body>
        <div class="badge-container pulse" onclick="handleBadgeClick()">
          <div class="badge-icon">AI</div>
          <div class="status-indicator"></div>
          <div class="tooltip">桌面AI助手</div>
        </div>

        <script>
          function handleBadgeClick() {
            // 添加点击反馈
            const container = document.querySelector('.badge-container');
            container.style.transform = 'scale(0.9)';
            setTimeout(() => {
              container.style.transform = '';
            }, 150);

            // 通过IPC发送点击事件
            if (window.electronAPI && window.electronAPI.onBadgeClick) {
              window.electronAPI.onBadgeClick();
            }
          }

          // 防止右键菜单
          document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
          });

          // 防止拖拽
          document.addEventListener('dragstart', (e) => {
            e.preventDefault();
          });

          // 防止选择文本
          document.addEventListener('selectstart', (e) => {
            e.preventDefault();
          });
        </script>
      </body>
      </html>
    `;

    this.badgeWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(badgeHTML)}`);
  }

  /**
   * 设置浮标事件
   */
  private setupBadgeEvents(): void {
    if (!this.badgeWindow) return;

    // 窗口关闭事件
    this.badgeWindow.on('closed', () => {
      this.badgeWindow = null;
    });

    // 点击事件
    this.badgeWindow.on('blur', () => {
      // 失去焦点时重新置顶
      if (this.badgeWindow) {
        this.badgeWindow.setAlwaysOnTop(true, 'floating');
      }
    });

    // 处理点击事件 - 使用多种方式确保点击事件能被正确捕获
    this.badgeWindow.webContents.on('before-input-event', (event, input) => {
      if (input.type === 'mouseDown') {
        if (this.onBadgeClick) {
          this.onBadgeClick();
        }
      }
    });

    // 额外的点击事件处理
    this.badgeWindow.on('focus', () => {
      // 当浮标获得焦点时也可以触发显示主窗口
      // 这里不自动触发，避免意外行为
    });
  }

  /**
   * 显示浮标
   */
  showBadge(): void {
    if (this.badgeWindow && !this.badgeWindow.isVisible()) {
      this.badgeWindow.show();
      this.badgeWindow.setAlwaysOnTop(true, 'floating');
    }
  }

  /**
   * 隐藏浮标
   */
  hideBadge(): void {
    if (this.badgeWindow && this.badgeWindow.isVisible()) {
      this.badgeWindow.hide();
    }
  }

  /**
   * 销毁浮标
   */
  destroyBadge(): void {
    if (this.badgeWindow) {
      this.badgeWindow.close();
      this.badgeWindow = null;
    }
  }

  /**
   * 检查浮标是否存在
   */
  isBadgeCreated(): boolean {
    return this.badgeWindow !== null;
  }

  /**
   * 检查浮标是否可见
   */
  isBadgeVisible(): boolean {
    return this.badgeWindow ? this.badgeWindow.isVisible() : false;
  }

  /**
   * 更新浮标位置
   */
  updateBadgePosition(): void {
    if (!this.badgeWindow) return;

    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    const badgeWidth = 60;
    const badgeHeight = 60;
    const x = width - badgeWidth - 20;
    const y = Math.floor(height * 0.3);

    this.badgeWindow.setPosition(x, y);
  }

  /**
   * 设置浮标状态（可用于显示不同的视觉状态）
   */
  setBadgeStatus(status: 'normal' | 'active' | 'thinking'): void {
    if (!this.badgeWindow) return;

    // 通过执行JavaScript来改变浮标的视觉状态
    let script = '';
    switch (status) {
      case 'active':
        script = `
          const container = document.querySelector('.badge-container');
          const indicator = document.querySelector('.status-indicator');
          container.style.background = 'linear-gradient(135deg, #52c41a 0%, #73d13d 50%, #95de64 100%)';
          indicator.style.background = '#52c41a';
          indicator.classList.remove('thinking');
        `;
        break;
      case 'thinking':
        script = `
          const container = document.querySelector('.badge-container');
          const indicator = document.querySelector('.status-indicator');
          container.style.background = 'linear-gradient(135deg, #faad14 0%, #ffc53d 50%, #ffd666 100%)';
          indicator.style.background = '#faad14';
          indicator.classList.add('thinking');
        `;
        break;
      default:
        script = `
          const container = document.querySelector('.badge-container');
          const indicator = document.querySelector('.status-indicator');
          container.style.background = 'linear-gradient(135deg, #1677ff 0%, #4096ff 50%, #69b1ff 100%)';
          indicator.style.background = '#52c41a';
          indicator.classList.remove('thinking');
        `;
    }

    this.badgeWindow.webContents.executeJavaScript(script).catch(console.error);
  }

  /**
   * 设置浮标文本（可以显示不同的文字）
   */
  setBadgeText(text: string): void {
    if (!this.badgeWindow) return;

    const script = `
      const icon = document.querySelector('.badge-icon');
      if (icon) {
        icon.textContent = '${text}';
      }
    `;

    this.badgeWindow.webContents.executeJavaScript(script).catch(console.error);
  }

  /**
   * 设置浮标提示文本
   */
  setBadgeTooltip(tooltip: string): void {
    if (!this.badgeWindow) return;

    const script = `
      const tooltipElement = document.querySelector('.tooltip');
      if (tooltipElement) {
        tooltipElement.textContent = '${tooltip}';
      }
    `;

    this.badgeWindow.webContents.executeJavaScript(script).catch(console.error);
  }
}
