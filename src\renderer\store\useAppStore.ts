import { create } from 'zustand';
import { Message, Session, AppConfig } from '@shared/types';

interface AppState {
  // 应用配置
  config: AppConfig | null;
  
  // 会话管理
  sessions: { [key: string]: Session };
  currentSessionId: string | null;
  
  // 消息管理
  messages: { [sessionId: string]: Message[] };
  
  // UI状态
  isLoading: boolean;
  isSidebarOpen: boolean;
  
  // 操作方法
  setConfig: (config: AppConfig) => void;
  setSessions: (sessions: { [key: string]: Session }) => void;
  setCurrentSession: (sessionId: string | null) => void;
  addSession: (session: Session) => void;
  updateSession: (sessionId: string, updates: Partial<Session>) => void;
  deleteSession: (sessionId: string) => void;
  
  setMessages: (sessionId: string, messages: Message[]) => void;
  addMessage: (message: Message) => void;
  updateMessage: (sessionId: string, messageId: string, updates: Partial<Message>) => void;
  
  setLoading: (loading: boolean) => void;
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  config: null,
  sessions: {},
  currentSessionId: null,
  messages: {},
  isLoading: false,
  isSidebarOpen: true,

  // 配置管理
  setConfig: (config) => set({ config }),

  // 会话管理
  setSessions: (sessions) => set({ sessions }),
  
  setCurrentSession: (sessionId) => set({ currentSessionId: sessionId }),
  
  addSession: (session) => set((state) => ({
    sessions: {
      ...state.sessions,
      [session.id]: session
    }
  })),
  
  updateSession: (sessionId, updates) => set((state) => ({
    sessions: {
      ...state.sessions,
      [sessionId]: {
        ...state.sessions[sessionId],
        ...updates
      }
    }
  })),
  
  deleteSession: (sessionId) => set((state) => {
    const newSessions = { ...state.sessions };
    delete newSessions[sessionId];
    
    const newMessages = { ...state.messages };
    delete newMessages[sessionId];
    
    return {
      sessions: newSessions,
      messages: newMessages,
      currentSessionId: state.currentSessionId === sessionId ? null : state.currentSessionId
    };
  }),

  // 消息管理
  setMessages: (sessionId, messages) => set((state) => ({
    messages: {
      ...state.messages,
      [sessionId]: messages
    }
  })),
  
  addMessage: (message) => set((state) => {
    const sessionMessages = state.messages[message.sessionId] || [];
    return {
      messages: {
        ...state.messages,
        [message.sessionId]: [...sessionMessages, message]
      }
    };
  }),
  
  updateMessage: (sessionId, messageId, updates) => set((state) => {
    const sessionMessages = state.messages[sessionId] || [];
    const updatedMessages = sessionMessages.map(msg =>
      msg.id === messageId ? { ...msg, ...updates } : msg
    );
    
    return {
      messages: {
        ...state.messages,
        [sessionId]: updatedMessages
      }
    };
  }),

  // UI状态管理
  setLoading: (loading) => set({ isLoading: loading }),
  
  toggleSidebar: () => set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),
  
  setSidebarOpen: (open) => set({ isSidebarOpen: open }),
}));
