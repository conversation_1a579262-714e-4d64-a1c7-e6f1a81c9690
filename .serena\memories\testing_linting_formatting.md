# 测试、代码检查和格式化

## 代码检查 (Linting)
项目配置了ESLint进行代码质量检查：

### ESLint配置
- **@typescript-eslint/eslint-plugin**: TypeScript专用规则
- **@typescript-eslint/parser**: TypeScript解析器
- **eslint-plugin-react**: React专用规则
- **eslint-plugin-react-hooks**: React Hooks规则

### 运行代码检查
```bash
# 手动运行ESLint（需要配置script）
npx eslint src/ --ext .ts,.tsx

# 检查并自动修复
npx eslint src/ --ext .ts,.tsx --fix
```

## TypeScript类型检查
```bash
# 类型检查
npx tsc --noEmit

# 监听模式类型检查
npx tsc --noEmit --watch
```

## 代码格式化
目前项目未配置Prettier，建议添加：

### 建议的Prettier配置
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

## 测试
目前项目未配置测试框架，建议添加：

### 建议的测试工具
- **Jest**: 测试框架
- **@testing-library/react**: React组件测试
- **@testing-library/jest-dom**: Jest DOM匹配器
- **electron-mock-ipc**: Electron IPC模拟

### 建议的测试命令
```bash
npm test              # 运行所有测试
npm run test:watch    # 监听模式运行测试
npm run test:coverage # 生成覆盖率报告
```

## 构建验证
```bash
# 构建前检查
npm run build         # 确保构建成功
npm start            # 确保应用能正常启动
```

## 任务完成后的检查清单
1. ✅ 代码构建成功 (`npm run build`)
2. ✅ 应用能正常启动 (`npm start`)
3. ✅ TypeScript类型检查通过 (`npx tsc --noEmit`)
4. ⚠️ ESLint检查通过（需要配置script）
5. ⚠️ 测试通过（需要添加测试）
6. ✅ 打包成功 (`npm run dist:win`)