import AutoLaunch from 'auto-launch';
import { app } from 'electron';
import { APP_CONFIG } from '@shared/constants';

export class AutoLaunchManager {
  private autoLauncher: AutoLaunch;

  constructor() {
    this.autoLauncher = new AutoLaunch({
      name: APP_CONFIG.APP_NAME,
      path: app.getPath('exe'),
      isHidden: true, // 启动时隐藏窗口
    });
  }

  async enable(): Promise<void> {
    try {
      const isEnabled = await this.autoLauncher.isEnabled();
      if (!isEnabled) {
        await this.autoLauncher.enable();
        console.log('自动启动已启用');
      }
    } catch (error) {
      console.error('启用自动启动失败:', error);
    }
  }

  async disable(): Promise<void> {
    try {
      const isEnabled = await this.autoLauncher.isEnabled();
      if (isEnabled) {
        await this.autoLauncher.disable();
        console.log('自动启动已禁用');
      }
    } catch (error) {
      console.error('禁用自动启动失败:', error);
    }
  }

  async isEnabled(): Promise<boolean> {
    try {
      return await this.autoLauncher.isEnabled();
    } catch (error) {
      console.error('检查自动启动状态失败:', error);
      return false;
    }
  }

  async toggle(): Promise<boolean> {
    try {
      const isEnabled = await this.isEnabled();
      if (isEnabled) {
        await this.disable();
        return false;
      } else {
        await this.enable();
        return true;
      }
    } catch (error) {
      console.error('切换自动启动状态失败:', error);
      return false;
    }
  }
}
