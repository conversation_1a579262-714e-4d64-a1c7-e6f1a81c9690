@echo off
echo 🚀 创建最终版桌面AI助手exe文件
echo.

echo 📋 当前功能状态：
echo ✅ 启动时隐藏到后台
echo ✅ 快捷键 Ctrl+Shift+A 显示/隐藏窗口
echo ✅ 快捷键 Ctrl+Shift+N 新建会话
echo ✅ 窗口关闭时隐藏而不退出
echo ⚠️  托盘图标暂时不可见（功能正常，只是图标显示问题）
echo.

echo 🔧 开始构建...

echo 📦 1. 构建渲染进程...
call npm run build:renderer
if %errorlevel% neq 0 (
    echo ❌ 渲染进程构建失败
    pause
    exit /b 1
)

echo 📦 2. 构建主进程...
call npm run build:main
if %errorlevel% neq 0 (
    echo ❌ 主进程构建失败
    pause
    exit /b 1
)

echo 📦 3. 清理旧的release目录...
if exist release rmdir /s /q release

echo 📦 4. 创建exe文件...
call npm run pack-no-sign
if %errorlevel% neq 0 (
    echo ⚠️ 打包失败，尝试其他方法...
    call npm run pack
    if %errorlevel% neq 0 (
        echo ❌ 所有打包方法都失败了
        echo.
        echo 💡 建议：
        echo 1. 以管理员身份运行此脚本
        echo 2. 或者使用便携版方案
        pause
        exit /b 1
    )
)

echo ✅ exe文件创建完成！
echo.
echo 📁 文件位置: release\win-unpacked\桌面AI助手.exe
echo.
echo 🎯 使用说明：
echo 1. 双击exe文件启动（不会显示窗口）
echo 2. 按 Ctrl+Shift+A 显示/隐藏窗口
echo 3. 按 Ctrl+Shift+N 创建新会话
echo 4. 关闭窗口时应用继续在后台运行
echo 5. 要完全退出，请在设置中选择退出
echo.
echo 📝 注意：托盘图标可能不可见，但所有功能都正常工作
echo.
pause
