# 项目结构详解

## 根目录结构
```
desktop-plugin/
├── src/                    # 源代码目录
├── assets/                 # 静态资源（图标等）
├── docs/                   # 文档和配置文件
├── scripts/                # 构建脚本
├── build/                  # 打包配置
├── public/                 # 公共文件
├── dist/                   # 构建输出目录
├── release/                # 打包输出目录
├── node_modules/           # 依赖包
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript配置
├── webpack.*.config.js     # Webpack配置
└── electron-builder.json   # 打包配置
```

## 源代码结构 (src/)
```
src/
├── main/                   # Electron主进程
│   ├── main.ts            # 应用程序入口
│   ├── window.ts          # 窗口管理
│   ├── tray.ts            # 系统托盘
│   ├── store.ts           # 数据存储
│   ├── aiService.ts       # AI服务
│   ├── autoLaunch.ts      # 自动启动
│   ├── shortcuts.ts       # 快捷键管理
│   └── preload.ts         # 预加载脚本
├── renderer/              # React渲染进程
│   ├── components/        # React组件
│   ├── hooks/            # 自定义Hooks
│   ├── store/            # 状态管理
│   ├── styles/           # 样式文件
│   ├── App.tsx           # 主应用组件
│   └── index.tsx         # 渲染进程入口
└── shared/               # 共享代码
    ├── types.ts          # 类型定义
    └── constants.ts      # 常量定义
```

## 关键文件说明
- **main.ts**: Electron应用主入口，管理应用生命周期
- **window.ts**: 窗口创建和管理逻辑
- **tray.ts**: 系统托盘功能实现
- **aiService.ts**: AI API调用和流式响应处理
- **App.tsx**: React应用主组件
- **types.ts**: 全局类型定义
- **constants.ts**: 应用常量定义

## 构建输出结构
```
dist/
├── main/                  # 主进程构建输出
│   ├── main.js           # 主进程入口
│   └── preload.js        # 预加载脚本
└── renderer/             # 渲染进程构建输出
    ├── index.html        # HTML入口
    └── bundle.js         # 渲染进程代码
```