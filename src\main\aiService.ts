import axios, { AxiosInstance } from 'axios';
import { BrowserWindow } from 'electron';
import { APIResponse, StreamChunk, Message } from '@shared/types';
import { API_CONFIG, IPC_CHANNELS } from '@shared/constants';
import { StoreManager } from './store';

export class AIService {
  private apiClient: AxiosInstance;
  private apiKey: string = '';
  private mainWindow: BrowserWindow | null = null;
  private storeManager: StoreManager | null = null;

  constructor() {
    this.apiClient = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  initialize(apiKey: string, mainWindow?: BrowserWindow, storeManager?: StoreManager): void {
    this.apiKey = apiKey;
    this.mainWindow = mainWindow || null;
    this.storeManager = storeManager || null;
    
    // 设置请求拦截器
    this.apiClient.interceptors.request.use((config) => {
      config.headers.Authorization = `Bearer ${this.apiKey}`;
      return config;
    });

    // 设置响应拦截器
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API请求失败:', error);
        this.sendErrorToRenderer(error.message);
        throw error;
      }
    );
  }

  async sendMessage(
    message: string,
    sessionId: string,
    conversationHistory: Message[] = []
  ): Promise<void> {
    try {
      console.log('🚀 AI服务开始处理消息:', { message, sessionId, historyLength: conversationHistory.length });

      // 检查API密钥
      if (!this.apiKey) {
        throw new Error('API密钥未配置');
      }

      // 构建消息历史
      const messages = this.buildMessageHistory(conversationHistory, message);
      console.log('📝 构建的消息历史:', messages);

      // 发送流式请求
      await this.sendStreamRequest(messages, sessionId);
    } catch (error) {
      console.error('❌ 发送消息失败:', error);
      this.sendErrorToRenderer(error instanceof Error ? error.message : '发送消息失败');
    }
  }

  // 获取会话历史消息的方法（从存储中获取）
  async getSessionHistory(sessionId: string): Promise<Message[]> {
    try {
      // 从StoreManager获取消息历史
      if (this.storeManager) {
        return this.storeManager.getMessages(sessionId);
      }
      return [];
    } catch (error) {
      console.error('获取会话历史失败:', error);
      return [];
    }
  }

  private buildMessageHistory(history: Message[], newMessage: string): any[] {
    const messages: any[] = [
      {
        role: 'system',
        content: '你是一个有用的AI助手，请用中文回答用户的问题。'
      }
    ];

    // 添加历史消息（最近的20条）
    const recentHistory = history.slice(-20);
    for (const msg of recentHistory) {
      if (msg.role !== 'system') {
        messages.push({
          role: msg.role,
          content: msg.content
        });
      }
    }

    // 添加新消息
    messages.push({
      role: 'user',
      content: newMessage
    });

    return messages;
  }

  private async sendStreamRequest(messages: any[], sessionId: string): Promise<void> {
    try {
      console.log('📡 发送API请求到:', API_CONFIG.BASE_URL + '/chat/completions');
      console.log('🔧 请求参数:', {
        model: API_CONFIG.DEFAULT_MODEL,
        messagesCount: messages.length,
        stream: true,
        temperature: API_CONFIG.DEFAULT_TEMPERATURE,
        max_tokens: API_CONFIG.DEFAULT_MAX_TOKENS
      });

      const response = await this.apiClient.post('/chat/completions', {
        model: API_CONFIG.DEFAULT_MODEL,
        messages: messages,
        stream: true,
        temperature: API_CONFIG.DEFAULT_TEMPERATURE,
        max_tokens: API_CONFIG.DEFAULT_MAX_TOKENS,
      }, {
        responseType: 'stream'
      });

      console.log('✅ API响应状态:', response.status);
      let assistantMessage = '';

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            
            if (data === '[DONE]') {
              this.sendStreamEndToRenderer(sessionId, assistantMessage);
              return;
            }

            try {
              const parsed: StreamChunk = JSON.parse(data);
              const content = parsed.choices[0]?.delta?.content;
              
              if (content) {
                assistantMessage += content;
                this.sendStreamChunkToRenderer(sessionId, content, assistantMessage);
              }
            } catch (parseError) {
              // 忽略解析错误，继续处理下一行
            }
          }
        }
      });

      response.data.on('error', (error: Error) => {
        console.error('流式响应错误:', error);
        this.sendErrorToRenderer(error.message);
      });

    } catch (error) {
      console.error('❌ 发送流式请求失败:', error);

      // 如果是网络错误，使用模拟响应
      const err = error as any;
      if (err.code === 'ENOTFOUND' || err.code === 'ECONNREFUSED' || err.code === 'ETIMEDOUT') {
        console.log('🔄 网络错误，切换到模拟模式');
        this.sendMockResponse(sessionId, messages[messages.length - 1]?.content || '');
        return;
      }

      throw error;
    }
  }

  private sendStreamChunkToRenderer(sessionId: string, chunk: string, fullMessage: string): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.send(IPC_CHANNELS.AI_STREAM_CHUNK, {
        sessionId,
        chunk,
        fullMessage
      });
    }
  }

  private sendStreamEndToRenderer(sessionId: string, fullMessage: string): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.send(IPC_CHANNELS.AI_STREAM_END, {
        sessionId,
        fullMessage
      });
    }
  }

  private sendErrorToRenderer(error: string): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.send(IPC_CHANNELS.AI_ERROR, error);
    }
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  // 测试API连接
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔍 测试API连接...');
      const response = await this.apiClient.post('/chat/completions', {
        model: API_CONFIG.DEFAULT_MODEL,
        messages: [
          {
            role: 'user',
            content: 'test'
          }
        ],
        stream: false,
        max_tokens: 1
      });

      console.log('✅ API连接测试成功');
      return true;
    } catch (error) {
      console.error('❌ API连接测试失败:', error);
      return false;
    }
  }

  // 发送模拟响应（当API不可用时）
  private sendMockResponse(sessionId: string, userMessage: string): void {
    console.log('🎭 发送模拟响应');

    const mockResponses = [
      '抱歉，当前无法连接到AI服务。这是一个模拟响应，用于测试应用功能。',
      '网络连接似乎有问题，无法访问真实的AI服务。请检查网络设置或稍后重试。',
      '当前处于离线模式。真实的AI服务暂时不可用，这是一个本地模拟响应。',
      '检测到网络问题，正在使用本地模拟模式。请确保网络连接正常后重试。'
    ];

    const response = mockResponses[Math.floor(Math.random() * mockResponses.length)];
    const words = response.split('');
    let currentText = '';

    // 模拟流式响应
    words.forEach((word, index) => {
      setTimeout(() => {
        currentText += word;
        this.sendStreamChunkToRenderer(sessionId, word, currentText);

        // 最后一个字符时结束
        if (index === words.length - 1) {
          setTimeout(() => {
            this.sendStreamEndToRenderer(sessionId, currentText);
          }, 100);
        }
      }, index * 50);
    });
  }
}
