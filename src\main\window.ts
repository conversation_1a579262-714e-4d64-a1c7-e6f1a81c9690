import { BrowserWindow, screen, app } from 'electron';
import * as path from 'path';
import { APP_CONFIG } from '@shared/constants';

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;

  async createMainWindow(showOnCreate: boolean = false): Promise<BrowserWindow> {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    // 计算窗口位置（右侧居中）
    const windowWidth = APP_CONFIG.WINDOW_DEFAULT_WIDTH;
    const windowHeight = APP_CONFIG.WINDOW_DEFAULT_HEIGHT;
    const x = width - windowWidth - 50; // 距离右边缘50px
    const y = Math.floor((height - windowHeight) / 2);

    this.mainWindow = new BrowserWindow({
      width: windowWidth,
      height: windowHeight,
      minWidth: APP_CONFIG.WINDOW_MIN_WIDTH,
      minHeight: APP_CONFIG.WINDOW_MIN_HEIGHT,
      x: x,
      y: y,
      frame: false, // 无边框窗口
      show: false, // 初始不显示
      resizable: true,
      maximizable: true,
      minimizable: true,
      closable: true,
      alwaysOnTop: false,
      skipTaskbar: false, // 显示在任务栏
      titleBarStyle: 'hidden',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
    });

    // 加载应用
    if (process.env.NODE_ENV === 'development') {
      await this.mainWindow.loadURL('http://localhost:3000');
      // 开发模式下打开开发者工具
      this.mainWindow.webContents.openDevTools();
    } else {
      await this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // 窗口事件处理
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // 窗口最小化时隐藏到托盘
    this.mainWindow.on('minimize', (event: Electron.Event) => {
      event.preventDefault();
      this.hideWindow();
    });

    // 窗口关闭时隐藏而不是退出
    this.mainWindow.on('close', (event) => {
      if (!(app as any).isQuiting) {
        event.preventDefault();
        this.hideWindow();
      }
    });

    // 窗口准备显示时的处理
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow && showOnCreate) {
        this.mainWindow.show();
        this.mainWindow.focus();
      }
    });

    return this.mainWindow;
  }

  showWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();

      // 确保窗口在最前面
      this.mainWindow.setAlwaysOnTop(true);
      this.mainWindow.setAlwaysOnTop(false);
    }
  }

  hideWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.hide();
    }
  }

  toggleWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isVisible()) {
        this.hideWindow();
      } else {
        this.showWindow();
      }
    }
  }

  minimizeWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.minimize();
    }
  }

  maximizeWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow.maximize();
      }
    }
  }

  closeWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.close();
    }
  }

  getWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  isWindowVisible(): boolean {
    return this.mainWindow ? this.mainWindow.isVisible() : false;
  }

  isWindowReady(): boolean {
    return this.mainWindow !== null;
  }

  bringToFront(): void {
    if (this.mainWindow && this.mainWindow.isVisible()) {
      this.mainWindow.focus();
      this.mainWindow.setAlwaysOnTop(true);
      this.mainWindow.setAlwaysOnTop(false);
    }
  }
}
