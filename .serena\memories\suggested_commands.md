# 建议的开发命令

## 项目初始化
```bash
# 安装依赖并创建图标
npm run setup

# 或者分步执行
npm install
npm run create-icons
```

## 开发模式
```bash
# 启动开发服务器（推荐）
npm run dev

# 手动启动（需要两个终端）
npm run dev:renderer  # 启动渲染进程开发服务器
npm run dev:main      # 启动主进程监听
npm start             # 启动Electron应用
```

## 构建命令
```bash
# 构建所有代码
npm run build

# 分别构建
npm run build:renderer  # 构建渲染进程
npm run build:main      # 构建主进程
```

## 打包和分发
```bash
# 一键构建和打包
npm run build-all

# 分步操作
npm run build      # 构建代码
npm run pack       # 打包为可执行文件
npm run dist:win   # 创建Windows安装程序
```

## 清理命令
```bash
# 清理构建文件
npm run clean
```

## Windows系统命令
```bash
# 文件操作
dir                    # 列出文件（相当于ls）
type filename          # 查看文件内容（相当于cat）
copy source dest       # 复制文件（相当于cp）
del filename           # 删除文件（相当于rm）
rmdir /s dirname       # 删除目录（相当于rm -rf）

# 进程管理
tasklist               # 查看进程（相当于ps）
taskkill /f /pid PID   # 终止进程（相当于kill）

# 网络
netstat -an            # 查看网络连接
ping hostname          # 网络连通性测试
```

## Git命令
```bash
git status             # 查看状态
git add .              # 添加所有更改
git commit -m "message" # 提交更改
git push               # 推送到远程
git pull               # 拉取更新
```