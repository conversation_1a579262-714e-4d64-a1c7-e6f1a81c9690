{"name": "desktop-ai-assistant", "version": "1.0.0", "description": "Windows桌面AI助手应用程序", "main": "dist/main/main.js", "homepage": "./", "scripts": {"dev": "npx concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "webpack serve --config webpack.renderer.config.js", "dev:main": "webpack --config webpack.main.config.js --watch", "build": "npm run build:renderer && npm run build:main", "build:renderer": "webpack --config webpack.renderer.config.js --mode production", "build:main": "webpack --config webpack.main.config.js --mode production", "start": "electron .", "pack": "electron-builder --dir", "pack-no-sign": "cross-env CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder --dir", "dist": "electron-builder", "dist:win": "electron-builder --win", "postinstall": "electron-builder install-app-deps", "create-icons": "node scripts/create-icons.js", "setup": "npm install && npm run create-icons", "build-all": "node scripts/build.js", "clean": "rimraf dist release"}, "keywords": ["electron", "react", "typescript", "ai", "assistant", "desktop"], "author": "Desktop AI Assistant", "license": "MIT", "devDependencies": {"@playwright/test": "^1.54.1", "@types/auto-launch": "^5.0.5", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "buffer": "^6.0.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "electron": "^28.3.3", "electron-builder": "^24.8.0", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "events": "^3.3.0", "html-webpack-plugin": "^5.6.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "playwright": "^1.54.1", "process": "^0.11.10", "rimraf": "^5.0.5", "stream-browserify": "^3.0.0", "style-loader": "^3.3.3", "to-ico": "^1.1.5", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "util": "^0.12.5", "webpack": "^5.89.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.15.1"}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.8", "auto-launch": "^5.0.6", "axios": "^1.6.2", "electron-store": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "build": {"appId": "com.desktop-ai-assistant.app", "productName": "桌面AI助手", "directories": {"output": "release"}, "files": ["dist/**/*", "package.json"], "extraResources": [{"from": "assets", "to": "assets"}], "win": {"target": "nsis", "icon": "assets/icons/icon.ico", "sign": null}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}