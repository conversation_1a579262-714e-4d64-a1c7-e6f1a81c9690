/**
 * 会话管理自动化测试
 * 测试会话历史、会话切换、数据持久化等会话管理功能
 */

const { test, expect } = require('@playwright/test');
const { _electron: electron } = require('playwright');
const path = require('path');
const { createTestHelpers } = require('../utils/helpers');
const { SELECTORS, COMBINED_SELECTORS } = require('../utils/selectors');

test.describe('会话管理功能测试', () => {
  let electronApp;
  let page;
  let helpers;

  test.beforeAll(async () => {
    electronApp = await electron.launch({
      args: [path.join(__dirname, '../../dist/main/main.js')],
      env: {
        NODE_ENV: 'test',
        ELECTRON_IS_DEV: '1'
      }
    });

    page = await electronApp.firstWindow();
    helpers = createTestHelpers(page);
    helpers.startConsoleLogging();
    
    await page.waitForLoadState('domcontentloaded');
    await helpers.waitForLoad();
  });

  test.afterAll(async () => {
    if (helpers) {
      await helpers.cleanup();
    }
    if (electronApp) {
      await electronApp.close();
    }
  });

  test('创建多个会话测试', async () => {
    // 创建第一个会话
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
    
    // 创建第二个会话
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
    
    // 创建第三个会话
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
    
    // 验证会话列表中有3个会话
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    const sessionCount = await sessionItems.count();
    expect(sessionCount).toBeGreaterThanOrEqual(3);
    
    // 验证最新会话是当前活跃会话
    const activeSession = page.locator(`${SELECTORS.sidebar.sessionItem}.active`);
    await expect(activeSession).toBeVisible();
    
    console.log('✅ 创建多个会话测试通过');
  });

  test('会话间消息隔离测试', async () => {
    // 确保有至少2个会话
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    let sessionCount = await sessionItems.count();
    
    while (sessionCount < 2) {
      await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
      await page.waitForTimeout(1000);
      sessionCount = await sessionItems.count();
    }
    
    // 在第一个会话中发送消息
    await sessionItems.first().click();
    await page.waitForTimeout(500);
    
    const firstSessionMessage = '第一个会话的消息';
    await helpers.safeType(SELECTORS.chat.inputField, firstSessionMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 切换到第二个会话
    await sessionItems.nth(1).click();
    await page.waitForTimeout(500);
    
    // 验证第二个会话中没有第一个会话的消息
    const messagesInSecondSession = page.locator(SELECTORS.chat.userMessage);
    const messageCount = await messagesInSecondSession.count();
    expect(messageCount).toBe(0);
    
    // 在第二个会话中发送不同的消息
    const secondSessionMessage = '第二个会话的消息';
    await helpers.safeType(SELECTORS.chat.inputField, secondSessionMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    await helpers.waitForElement(SELECTORS.chat.userMessage);
    
    // 切换回第一个会话
    await sessionItems.first().click();
    await page.waitForTimeout(500);
    
    // 验证第一个会话的消息仍然存在
    const messagesInFirstSession = page.locator(SELECTORS.chat.userMessage);
    const firstSessionMessageCount = await messagesInFirstSession.count();
    expect(firstSessionMessageCount).toBeGreaterThan(0);
    
    // 验证消息内容正确
    const lastMessageInFirst = messagesInFirstSession.last();
    await helpers.assertTextContent(lastMessageInFirst.locator(SELECTORS.message.content), firstSessionMessage);
    
    console.log('✅ 会话间消息隔离测试通过');
  });

  test('会话历史保存测试', async () => {
    // 创建新会话并发送消息
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
    
    const testMessages = [
      '第一条历史消息',
      '第二条历史消息',
      '第三条历史消息'
    ];
    
    // 发送多条消息
    for (const message of testMessages) {
      await helpers.safeType(SELECTORS.chat.inputField, message);
      await helpers.safeClick(SELECTORS.chat.sendButton);
      await page.waitForTimeout(500);
    }
    
    // 等待所有消息发送完成
    await page.waitForTimeout(1000);
    
    // 验证消息都已保存
    const userMessages = page.locator(SELECTORS.chat.userMessage);
    const messageCount = await userMessages.count();
    expect(messageCount).toBe(testMessages.length);
    
    // 验证消息内容正确
    for (let i = 0; i < testMessages.length; i++) {
      const message = userMessages.nth(i);
      await helpers.assertTextContent(message.locator(SELECTORS.message.content), testMessages[i]);
    }
    
    // 切换到其他会话再切换回来，验证消息持久化
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    if (await sessionItems.count() > 1) {
      // 切换到其他会话
      await sessionItems.first().click();
      await page.waitForTimeout(500);
      
      // 切换回当前会话
      await sessionItems.last().click();
      await page.waitForTimeout(500);
      
      // 验证消息仍然存在
      const persistedMessages = page.locator(SELECTORS.chat.userMessage);
      const persistedCount = await persistedMessages.count();
      expect(persistedCount).toBe(testMessages.length);
    }
    
    console.log('✅ 会话历史保存测试通过');
  });

  test('会话列表排序测试', async () => {
    // 确保有多个会话
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    let sessionCount = await sessionItems.count();
    
    while (sessionCount < 3) {
      await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
      await page.waitForTimeout(1000);
      sessionCount = await sessionItems.count();
    }
    
    // 在第一个会话中发送消息（更新其时间戳）
    await sessionItems.first().click();
    await page.waitForTimeout(500);
    
    await helpers.safeType(SELECTORS.chat.inputField, '更新第一个会话');
    await helpers.safeClick(SELECTORS.chat.sendButton);
    await page.waitForTimeout(1000);
    
    // 验证第一个会话移动到列表顶部（最近使用）
    const updatedSessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    const firstSession = updatedSessionItems.first();
    await expect(firstSession).toHaveClass(/active/);
    
    console.log('✅ 会话列表排序测试通过');
  });

  test('会话标题显示测试', async () => {
    // 创建新会话
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
    
    // 发送一条消息
    const testMessage = '这是用于生成标题的测试消息';
    await helpers.safeType(SELECTORS.chat.inputField, testMessage);
    await helpers.safeClick(SELECTORS.chat.sendButton);
    await page.waitForTimeout(2000);
    
    // 验证会话在侧边栏中有标题显示
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    const currentSession = sessionItems.last();
    
    // 检查会话项是否有文本内容
    const sessionText = await currentSession.textContent();
    expect(sessionText.length).toBeGreaterThan(0);
    
    // 验证会话标题不是默认的空标题
    expect(sessionText.trim()).not.toBe('');
    expect(sessionText.trim()).not.toBe('新会话');
    
    console.log('✅ 会话标题显示测试通过');
  });

  test('会话消息计数测试', async () => {
    // 创建新会话
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
    
    // 发送几条消息
    const messageCount = 3;
    for (let i = 1; i <= messageCount; i++) {
      await helpers.safeType(SELECTORS.chat.inputField, `测试消息 ${i}`);
      await helpers.safeClick(SELECTORS.chat.sendButton);
      await page.waitForTimeout(500);
    }
    
    // 等待消息处理完成
    await page.waitForTimeout(1000);
    
    // 验证消息列表中的消息数量
    const userMessages = page.locator(SELECTORS.chat.userMessage);
    const actualMessageCount = await userMessages.count();
    expect(actualMessageCount).toBe(messageCount);
    
    // 检查会话项中是否显示消息计数（如果实现了）
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    const currentSession = sessionItems.last();
    const sessionText = await currentSession.textContent();
    
    // 如果会话项显示消息计数，验证其正确性
    if (sessionText.includes('条消息')) {
      expect(sessionText).toContain(`${messageCount} 条消息`);
    }
    
    console.log('✅ 会话消息计数测试通过');
  });

  test('空会话处理测试', async () => {
    // 创建新会话但不发送消息
    await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
    await page.waitForTimeout(1000);
    
    // 验证空会话状态
    const emptyState = page.locator('[data-testid="chat-empty-state"]');
    if (await emptyState.count() > 0) {
      // 如果有空状态提示，验证其显示
      await expect(emptyState).toBeVisible();
    } else {
      // 验证消息列表为空
      const userMessages = page.locator(SELECTORS.chat.userMessage);
      const messageCount = await userMessages.count();
      expect(messageCount).toBe(0);
    }
    
    // 验证输入框仍然可用
    const inputField = page.locator(SELECTORS.chat.inputField);
    await expect(inputField).toBeEnabled();
    
    console.log('✅ 空会话处理测试通过');
  });

  test('会话切换性能测试', async () => {
    // 确保有多个会话
    const sessionItems = page.locator(SELECTORS.sidebar.sessionItem);
    let sessionCount = await sessionItems.count();
    
    while (sessionCount < 3) {
      await helpers.safeClick(SELECTORS.sidebar.newSessionButton);
      await page.waitForTimeout(1000);
      sessionCount = await sessionItems.count();
    }
    
    // 测试快速切换会话的性能
    const switchTimes = [];
    
    for (let i = 0; i < 3; i++) {
      const startTime = Date.now();
      
      // 切换到不同的会话
      await sessionItems.nth(i % sessionCount).click();
      
      // 等待界面更新
      await page.waitForTimeout(100);
      
      const endTime = Date.now();
      switchTimes.push(endTime - startTime);
    }
    
    // 验证切换时间合理（平均小于1秒）
    const averageSwitchTime = switchTimes.reduce((a, b) => a + b, 0) / switchTimes.length;
    expect(averageSwitchTime).toBeLessThan(1000);
    
    console.log(`✅ 会话切换性能测试通过 (平均切换时间: ${averageSwitchTime.toFixed(2)}ms)`);
  });
});
