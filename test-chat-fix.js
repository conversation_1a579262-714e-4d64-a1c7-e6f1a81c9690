// 测试聊天修复的脚本
const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 开始测试聊天功能修复...');

// 检查必要文件是否存在
const fs = require('fs');
const requiredFiles = [
  'src/main/aiService.ts',
  'src/main/main.ts',
  'docs/env'
];

console.log('📋 检查必要文件...');
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 不存在`);
  }
}

// 检查API密钥
try {
  const envContent = fs.readFileSync('./docs/env', 'utf8');
  const apiKey = envContent.split('=')[1]?.trim();
  if (apiKey && apiKey.length > 10) {
    console.log('✅ API密钥配置正确');
  } else {
    console.log('❌ API密钥配置有问题');
  }
} catch (error) {
  console.log('❌ 无法读取API密钥:', error.message);
}

// 检查依赖
console.log('\n📦 检查依赖安装...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = ['axios', 'electron', 'electron-store'];

for (const dep of requiredDeps) {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`✅ ${dep} - 已安装`);
  } else {
    console.log(`❌ ${dep} - 未安装`);
  }
}

// 构建应用
console.log('\n🔨 开始构建应用...');

function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
      process.stdout.write(data);
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
      process.stderr.write(data);
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr });
      } else {
        reject(new Error(`命令执行失败，退出码: ${code}\n${stderr}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function buildAndTest() {
  try {
    // 构建主进程
    console.log('🔧 构建主进程...');
    await runCommand('npx', ['webpack', '--config', 'webpack.main.config.js'], {
      timeout: 60000
    });
    console.log('✅ 主进程构建完成');

    // 构建渲染进程
    console.log('🔧 构建渲染进程...');
    await runCommand('npx', ['webpack', '--config', 'webpack.renderer.config.js', '--mode', 'production'], {
      timeout: 120000
    });
    console.log('✅ 渲染进程构建完成');

    console.log('\n🎉 构建成功！');
    console.log('\n📝 测试说明：');
    console.log('1. 运行 "npm start" 启动应用');
    console.log('2. 创建新会话');
    console.log('3. 发送测试消息');
    console.log('4. 观察控制台输出和聊天响应');
    console.log('\n💡 如果看到网络错误，应用会自动切换到模拟模式');
    console.log('💡 如果网络正常，会显示真实的AI响应');

  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    console.log('\n🔧 故障排除建议：');
    console.log('1. 确保所有依赖已安装: npm install');
    console.log('2. 检查TypeScript配置');
    console.log('3. 查看详细错误信息');
  }
}

// 运行构建和测试
buildAndTest();
