# 开发指南和最佳实践

## 添加新功能的流程

### 1. 规划阶段
- 在 `src/shared/types.ts` 中定义相关类型
- 在 `src/shared/constants.ts` 中添加必要常量
- 设计IPC通信接口（如果需要）

### 2. 主进程开发
- 在 `src/main/` 目录下实现业务逻辑
- 遵循现有的类结构模式
- 添加必要的IPC处理器

### 3. 渲染进程开发
- 在 `src/renderer/components/` 中创建UI组件
- 在 `src/renderer/hooks/` 中创建自定义Hooks
- 使用Zustand进行状态管理

### 4. 进程间通信
- 使用预定义的IPC_CHANNELS常量
- 在preload.ts中暴露安全的API
- 在渲染进程中通过window.electronAPI调用

## 设计模式和原则

### 主进程架构
- **单例模式**: 每个管理器类使用单例模式
- **模块化**: 按功能拆分为独立的管理器类
- **事件驱动**: 使用Electron的事件系统

### 渲染进程架构
- **组件化**: 使用React函数组件
- **Hooks模式**: 自定义Hooks封装业务逻辑
- **状态管理**: Zustand进行全局状态管理

### 代码组织原则
- **关注点分离**: 业务逻辑与UI分离
- **单一职责**: 每个模块只负责一个功能
- **依赖注入**: 通过构造函数注入依赖

## 调试技巧

### 主进程调试
```bash
# 启动时显示主进程调试信息
npm run dev:main
# 查看终端输出的日志
```

### 渲染进程调试
- 开发模式自动打开DevTools
- 使用console.log进行调试
- 使用React DevTools扩展

### IPC通信调试
```typescript
// 在主进程中添加日志
ipcMain.handle('channel-name', (event, ...args) => {
  console.log('IPC调用:', 'channel-name', args);
  // 处理逻辑
});

// 在渲染进程中添加日志
const result = await window.electronAPI.someMethod(data);
console.log('IPC结果:', result);
```

## 性能优化建议

### 主进程优化
- 避免阻塞主线程的同步操作
- 使用异步API处理文件和网络操作
- 合理管理窗口和资源

### 渲染进程优化
- 使用React.memo优化组件渲染
- 合理使用useCallback和useMemo
- 避免不必要的状态更新

### 内存管理
- 及时清理事件监听器
- 避免内存泄漏
- 合理使用缓存

## 安全考虑

### Electron安全
- 禁用node集成在渲染进程中
- 使用contextIsolation
- 通过preload脚本暴露安全API

### API安全
- 安全存储API密钥
- 验证用户输入
- 处理网络请求错误