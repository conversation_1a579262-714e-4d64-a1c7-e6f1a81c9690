# 桌面AI助手项目概述

## 项目目的
这是一个基于Electron + React + TypeScript的Windows桌面AI助手应用程序，集成通义千问API，提供智能对话功能。

## 主要功能
- 🤖 AI对话：集成通义千问API，支持智能对话
- 💬 会话管理：支持多会话管理，会话历史持久化
- 🎨 现代化UI：基于Ant Design的现代化用户界面
- 📌 系统托盘：常驻系统托盘，随时可用
- 🚀 自动启动：支持开机自动启动
- 💾 本地存储：聊天历史本地安全存储
- 🔄 流式响应：支持AI流式响应，实时显示

## 技术栈
- **前端框架**: React 18 + TypeScript
- **桌面框架**: Electron 28
- **UI组件库**: Ant Design 5
- **状态管理**: Zustand
- **构建工具**: Webpack 5
- **AI API**: 通义千问 (OpenAI兼容接口)
- **HTTP客户端**: Axios
- **数据存储**: electron-store

## 项目状态
项目已完成开发，包含完整的功能实现、构建配置和部署脚本。