import Store from 'electron-store';
import { AppConfig, Session, Message } from '@shared/types';
import { STORAGE_KEYS, API_CONFIG, APP_CONFIG } from '@shared/constants';
import * as fs from 'fs';
import * as path from 'path';

export class StoreManager {
  private store: Store;
  private configStore: Store<AppConfig>;
  private sessionsStore: Store<{ [key: string]: Session }>;
  private messagesStore: Store<{ [key: string]: Message[] }>;

  constructor() {
    // 主配置存储
    this.store = new Store({
      name: 'main-config'
    });

    // 应用配置存储
    this.configStore = new Store<AppConfig>({
      name: 'app-config',
      defaults: this.getDefaultConfig()
    });

    // 会话存储
    this.sessionsStore = new Store<{ [key: string]: Session }>({
      name: 'sessions',
      defaults: {}
    });

    // 消息存储
    this.messagesStore = new Store<{ [key: string]: Message[] }>({
      name: 'messages',
      defaults: {}
    });

    this.initializeConfig();
  }

  private getDefaultConfig(): AppConfig {
    // 从环境文件读取API密钥
    const apiKey = this.readApiKeyFromEnv();
    
    return {
      apiKey: apiKey,
      model: API_CONFIG.DEFAULT_MODEL,
      temperature: API_CONFIG.DEFAULT_TEMPERATURE,
      maxTokens: API_CONFIG.DEFAULT_MAX_TOKENS,
      autoStart: false,
      showOnStartup: false, // 默认启动时不显示窗口，隐藏到托盘
      windowPosition: {
        x: 100,
        y: 100,
        width: APP_CONFIG.WINDOW_DEFAULT_WIDTH,
        height: APP_CONFIG.WINDOW_DEFAULT_HEIGHT
      }
    };
  }

  private readApiKeyFromEnv(): string {
    try {
      const envPath = path.join(process.cwd(), 'docs', 'env');
      if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf-8');
        const match = envContent.match(/api-key=(.+)/);
        if (match && match[1]) {
          return match[1].trim();
        }
      }
    } catch (error) {
      console.error('读取API密钥失败:', error);
    }
    return '';
  }

  private initializeConfig(): void {
    // 如果配置不存在，创建默认配置
    if (!this.configStore.has('apiKey')) {
      const defaultConfig = this.getDefaultConfig();
      this.configStore.store = defaultConfig;
    }
  }

  // 通用存储方法
  get(key: string): any {
    return this.store.get(key);
  }

  set(key: string, value: any): void {
    this.store.set(key, value);
  }

  delete(key: string): void {
    this.store.delete(key);
  }

  // 应用配置管理
  getAppConfig(): AppConfig {
    return this.configStore.store;
  }

  setAppConfig(config: Partial<AppConfig>): void {
    this.configStore.store = { ...this.configStore.store, ...config };
  }

  // 会话管理
  getSessions(): { [key: string]: Session } {
    return this.sessionsStore.store;
  }

  getSession(sessionId: string): Session | undefined {
    return this.sessionsStore.get(sessionId);
  }

  saveSession(session: Session): void {
    this.sessionsStore.set(session.id, session);
  }

  deleteSession(sessionId: string): void {
    this.sessionsStore.delete(sessionId);
    this.messagesStore.delete(sessionId);
  }

  // 消息管理
  getMessages(sessionId: string): Message[] {
    return this.messagesStore.get(sessionId, []);
  }

  saveMessage(message: Message): void {
    const messages = this.getMessages(message.sessionId);
    messages.push(message);
    this.messagesStore.set(message.sessionId, messages);
    
    // 更新会话信息
    const session = this.getSession(message.sessionId);
    if (session) {
      session.updatedAt = Date.now();
      session.messageCount = messages.length;
      this.saveSession(session);
    }
  }

  saveMessages(sessionId: string, messages: Message[]): void {
    this.messagesStore.set(sessionId, messages);
    
    // 更新会话信息
    const session = this.getSession(sessionId);
    if (session) {
      session.updatedAt = Date.now();
      session.messageCount = messages.length;
      this.saveSession(session);
    }
  }

  // 应用状态保存
  saveAppState(): void {
    // 保存当前应用状态，如窗口位置等
    console.log('应用状态已保存');
  }

  // 清理数据
  clearAllData(): void {
    this.sessionsStore.clear();
    this.messagesStore.clear();
  }

  // 导出数据
  exportData(): { sessions: any, messages: any } {
    return {
      sessions: this.sessionsStore.store,
      messages: this.messagesStore.store
    };
  }

  // 导入数据
  importData(data: { sessions: any, messages: any }): void {
    if (data.sessions) {
      this.sessionsStore.store = data.sessions;
    }
    if (data.messages) {
      this.messagesStore.store = data.messages;
    }
  }
}
