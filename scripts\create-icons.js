// 创建图标文件
const fs = require('fs');
const path = require('path');
const toIco = require('to-ico');

// 确保图标目录存在
const iconsDir = path.join(__dirname, '../assets/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// 创建简单的SVG图标内容
const createSVGIcon = (size, color = '#1677ff', text = 'AI') => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" rx="${size/8}" fill="${color}"/>
  <text x="50%" y="50%" text-anchor="middle" dy="0.35em" fill="white" font-family="Arial, sans-serif" font-size="${size/3}" font-weight="bold">${text}</text>
</svg>`;
};

// 创建PNG图像数据（简单的像素数据）
const createPNGBuffer = (size, color = '#1677ff') => {
  // 创建一个简单的PNG缓冲区
  // 这里我们创建一个基本的蓝色方块
  const width = size;
  const height = size;
  const channels = 4; // RGBA

  // PNG文件头
  const pngSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);

  // 简化：创建一个基本的蓝色方块
  const pixelData = Buffer.alloc(width * height * channels);

  // 解析颜色
  const r = parseInt(color.slice(1, 3), 16);
  const g = parseInt(color.slice(3, 5), 16);
  const b = parseInt(color.slice(5, 7), 16);

  for (let i = 0; i < width * height; i++) {
    const offset = i * channels;
    pixelData[offset] = r;     // Red
    pixelData[offset + 1] = g; // Green
    pixelData[offset + 2] = b; // Blue
    pixelData[offset + 3] = 255; // Alpha
  }

  return pixelData;
};

// 使用to-ico创建高质量ICO文件
const createICOFromPNG = async (pngBuffers) => {
  try {
    return await toIco(pngBuffers);
  } catch (error) {
    console.error('创建ICO文件失败:', error);
    return null;
  }
};

// 创建图标文件
const createIcon = async (filename, size, color, text, createICO = false) => {
  const svgContent = createSVGIcon(size, color, text);
  const svgPath = path.join(iconsDir, filename.replace('.ico', '.svg').replace('.png', '.svg'));
  fs.writeFileSync(svgPath, svgContent);
  console.log(`Created ${svgPath}`);

  // 如果需要创建ICO文件
  if (createICO && filename.endsWith('.ico')) {
    // 创建多个尺寸的PNG缓冲区用于ICO
    const sizes = [16, 32, 48, 64, 128, 256];
    const pngBuffers = sizes.map(s => createPNGBuffer(s, color));

    const icoBuffer = await createICOFromPNG(pngBuffers);
    if (icoBuffer) {
      const icoPath = path.join(iconsDir, filename);
      fs.writeFileSync(icoPath, icoBuffer);
      console.log(`Created ${icoPath}`);
    }
  }
};

// 主函数
const main = async () => {
  try {
    // 创建各种图标
    await createIcon('icon.ico', 256, '#1677ff', 'AI', true);
    await createIcon('tray.ico', 32, '#666666', 'AI', true);
    await createIcon('tray-thinking.ico', 32, '#52c41a', '...', true);
    await createIcon('tray-error.ico', 32, '#ff4d4f', '!', true);

    // 创建PNG版本
    await createIcon('icon.png', 512, '#1677ff', 'AI');
    await createIcon('tray.png', 32, '#666666', 'AI');
    await createIcon('tray-thinking.png', 32, '#52c41a', '...');
    await createIcon('tray-error.png', 32, '#ff4d4f', '!');

    console.log('图标文件创建完成！');
  } catch (error) {
    console.error('创建图标时出错:', error);
  }
};

// 运行主函数
main();
