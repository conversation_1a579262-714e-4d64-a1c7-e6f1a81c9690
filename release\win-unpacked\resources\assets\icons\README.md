# 图标文件说明

这个目录包含应用程序所需的图标文件。

## 需要的图标文件：

### Windows 图标
- `icon.ico` - 主应用图标 (256x256, 128x128, 64x64, 48x48, 32x32, 16x16)
- `tray.ico` - 系统托盘图标 (16x16, 32x32)
- `tray-thinking.ico` - 思考状态托盘图标
- `tray-error.ico` - 错误状态托盘图标

### macOS 图标 (如果需要跨平台)
- `icon.icns` - 主应用图标
- `trayTemplate.png` - 系统托盘图标 (16x16, 32x32)
- `tray-thinkingTemplate.png` - 思考状态托盘图标
- `tray-errorTemplate.png` - 错误状态托盘图标

### Linux 图标 (如果需要跨平台)
- `icon.png` - 主应用图标 (512x512)
- `tray.png` - 系统托盘图标 (16x16, 32x32)
- `tray-thinking.png` - 思考状态托盘图标
- `tray-error.png` - 错误状态托盘图标

## 图标设计建议：

1. **主图标**: 应该体现AI助手的特征，建议使用机器人、对话气泡或AI相关的图形
2. **托盘图标**: 应该简洁明了，在小尺寸下仍然清晰可见
3. **状态图标**: 
   - 思考状态：可以添加动画效果或思考符号
   - 错误状态：使用红色或警告色调

## 临时解决方案：

在开发阶段，可以使用简单的几何图形或文字作为占位符图标。
