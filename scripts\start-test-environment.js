/**
 * 桌面AI助手测试环境启动脚本
 * 自动启动开发环境并准备Playwright测试
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');

class TestEnvironmentManager {
  constructor() {
    this.processes = [];
    this.isShuttingDown = false;
    this.devServerPort = 3000;
    this.devServerUrl = `http://localhost:${this.devServerPort}`;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = {
      info: '📋',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      process: '🔄'
    }[type] || '📋';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  async checkPrerequisites() {
    this.log('检查环境前置条件...', 'process');
    
    // 检查必要文件
    const requiredFiles = [
      'package.json',
      'webpack.main.config.js',
      'webpack.renderer.config.js',
      'docs/env',
      'src/main/main.ts',
      'src/renderer/App.tsx'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        this.log(`缺少必要文件: ${file}`, 'error');
        return false;
      }
    }

    // 检查API配置
    try {
      const envContent = fs.readFileSync('./docs/env', 'utf8');
      const apiKey = envContent.split('=')[1]?.trim();
      if (!apiKey || apiKey.length < 10) {
        this.log('API密钥配置无效', 'warning');
      } else {
        this.log('API密钥配置正确', 'success');
      }
    } catch (error) {
      this.log(`API配置检查失败: ${error.message}`, 'error');
    }

    // 检查node_modules
    if (!fs.existsSync('node_modules')) {
      this.log('依赖未安装，请先运行 npm install', 'error');
      return false;
    }

    this.log('环境前置条件检查完成', 'success');
    return true;
  }

  async buildApplication() {
    this.log('开始构建应用...', 'process');
    
    // 构建主进程
    await this.runCommand('npx', ['webpack', '--config', 'webpack.main.config.js'], {
      description: '构建主进程',
      timeout: 60000
    });

    // 检查构建结果
    if (!fs.existsSync('dist/main/main.js')) {
      throw new Error('主进程构建失败');
    }

    this.log('应用构建完成', 'success');
  }

  async startDevServer() {
    this.log('启动前端开发服务器...', 'process');
    
    return new Promise((resolve, reject) => {
      const devServer = spawn('npx', ['webpack', 'serve', '--config', 'webpack.renderer.config.js'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      this.processes.push({
        name: 'webpack-dev-server',
        process: devServer,
        port: this.devServerPort
      });

      let serverReady = false;
      
      devServer.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('webpack compiled') || output.includes('Local:')) {
          if (!serverReady) {
            serverReady = true;
            this.log(`前端开发服务器启动成功: ${this.devServerUrl}`, 'success');
            resolve();
          }
        }
      });

      devServer.stderr.on('data', (data) => {
        const error = data.toString();
        if (error.includes('Error:') || error.includes('ERROR')) {
          this.log(`开发服务器错误: ${error}`, 'error');
        }
      });

      devServer.on('close', (code) => {
        if (code !== 0 && !this.isShuttingDown) {
          this.log(`开发服务器异常退出，代码: ${code}`, 'error');
        }
      });

      // 超时检查
      setTimeout(() => {
        if (!serverReady) {
          reject(new Error('开发服务器启动超时'));
        }
      }, 30000);
    });
  }

  async waitForServer(url, maxAttempts = 30) {
    this.log(`等待服务器就绪: ${url}`, 'process');
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        await this.checkUrl(url);
        this.log('服务器就绪', 'success');
        return true;
      } catch (error) {
        await this.sleep(1000);
      }
    }
    
    throw new Error(`服务器启动超时: ${url}`);
  }

  checkUrl(url) {
    return new Promise((resolve, reject) => {
      const request = http.get(url, (res) => {
        resolve(res.statusCode === 200);
      });
      
      request.on('error', reject);
      request.setTimeout(5000, () => {
        request.destroy();
        reject(new Error('请求超时'));
      });
    });
  }

  async startElectronApp() {
    this.log('启动Electron应用...', 'process');
    
    return new Promise((resolve) => {
      const electron = spawn('npm', ['start'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      this.processes.push({
        name: 'electron-app',
        process: electron
      });

      let appReady = false;

      electron.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Electron app is ready') || output.includes('ready-to-show')) {
          if (!appReady) {
            appReady = true;
            this.log('Electron应用启动成功', 'success');
            resolve();
          }
        }
      });

      electron.stderr.on('data', (data) => {
        const error = data.toString();
        if (error.includes('Error:') || error.includes('ERROR')) {
          this.log(`Electron错误: ${error}`, 'error');
        }
      });

      // 给Electron一些时间启动
      setTimeout(() => {
        if (!appReady) {
          this.log('Electron应用启动（假设成功）', 'success');
          resolve();
        }
      }, 5000);
    });
  }

  async runCommand(command, args, options = {}) {
    const { description, timeout = 30000 } = options;
    
    if (description) {
      this.log(description, 'process');
    }

    return new Promise((resolve, reject) => {
      const child = spawn(command, args, {
        stdio: 'pipe',
        shell: true
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`命令执行失败，退出码: ${code}\n${stderr}`));
        }
      });

      if (timeout) {
        setTimeout(() => {
          child.kill();
          reject(new Error('命令执行超时'));
        }, timeout);
      }
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async shutdown() {
    if (this.isShuttingDown) return;
    
    this.isShuttingDown = true;
    this.log('正在关闭测试环境...', 'process');

    for (const proc of this.processes) {
      try {
        this.log(`关闭 ${proc.name}...`, 'process');
        proc.process.kill('SIGTERM');
        
        // 等待进程关闭
        await this.sleep(2000);
        
        if (!proc.process.killed) {
          proc.process.kill('SIGKILL');
        }
      } catch (error) {
        this.log(`关闭进程失败: ${error.message}`, 'error');
      }
    }

    this.log('测试环境已关闭', 'success');
  }

  async start() {
    try {
      this.log('🚀 启动桌面AI助手测试环境', 'info');
      
      // 检查前置条件
      const prereqsOk = await this.checkPrerequisites();
      if (!prereqsOk) {
        throw new Error('环境前置条件检查失败');
      }

      // 构建应用
      await this.buildApplication();

      // 启动开发服务器
      await this.startDevServer();
      
      // 等待服务器就绪
      await this.waitForServer(this.devServerUrl);

      // 启动Electron应用
      await this.startElectronApp();

      this.log('🎉 测试环境启动完成！', 'success');
      this.log('', 'info');
      this.log('📋 服务状态:', 'info');
      this.log(`  • 前端开发服务器: ${this.devServerUrl}`, 'info');
      this.log('  • Electron应用: 已启动', 'info');
      this.log('  • API服务: 通义千问API已配置', 'info');
      this.log('', 'info');
      this.log('💡 现在可以运行Playwright测试了！', 'info');
      this.log('💡 按 Ctrl+C 关闭测试环境', 'info');

      return {
        devServerUrl: this.devServerUrl,
        processes: this.processes
      };

    } catch (error) {
      this.log(`启动失败: ${error.message}`, 'error');
      await this.shutdown();
      throw error;
    }
  }
}

// 处理进程退出
process.on('SIGINT', async () => {
  console.log('\n收到退出信号...');
  if (global.testEnvManager) {
    await global.testEnvManager.shutdown();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  if (global.testEnvManager) {
    await global.testEnvManager.shutdown();
  }
  process.exit(0);
});

// 如果直接运行此脚本
if (require.main === module) {
  const manager = new TestEnvironmentManager();
  global.testEnvManager = manager;
  
  manager.start().catch((error) => {
    console.error('启动失败:', error.message);
    process.exit(1);
  });
}

module.exports = TestEnvironmentManager;
