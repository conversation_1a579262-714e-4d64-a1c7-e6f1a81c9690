// 验证浮标功能的脚本
const { exec } = require('child_process');

console.log('🔍 验证桌面浮标功能...');

// 检查是否有Electron进程在运行
exec('tasklist /fi "imagename eq electron.exe"', (error, stdout, stderr) => {
  if (error) {
    console.error('❌ 检查进程时出错:', error);
    return;
  }
  
  if (stdout.includes('electron.exe')) {
    console.log('✅ 检测到Electron应用正在运行');
    console.log('📋 请手动验证以下功能：');
    console.log('1. 桌面右侧是否出现蓝色圆形浮标');
    console.log('2. 浮标是否显示"AI"文字');
    console.log('3. 鼠标悬停时浮标是否有放大效果');
    console.log('4. 点击浮标是否能显示主窗口');
    console.log('5. 浮标是否始终保持在最前面');
    console.log('');
    console.log('💡 如果浮标没有出现，请检查：');
    console.log('- 应用是否正常启动');
    console.log('- 控制台是否有错误信息');
    console.log('- 屏幕分辨率是否正确检测');
  } else {
    console.log('❌ 未检测到Electron应用运行');
    console.log('请先运行: npm start');
  }
});

// 检查窗口信息
setTimeout(() => {
  exec('tasklist /fi "imagename eq 桌面AI助手.exe"', (error, stdout, stderr) => {
    if (!error && stdout.includes('桌面AI助手.exe')) {
      console.log('✅ 检测到桌面AI助手正在运行');
    }
  });
}, 1000);
