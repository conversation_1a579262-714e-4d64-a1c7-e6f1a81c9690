# 系统托盘功能测试指南

## 测试项目清单

### 1. 启动行为测试 ✅
- [x] 应用启动时不显示主窗口
- [x] 系统托盘中出现应用图标
- [x] 托盘图标显示正确的提示文本

### 2. 托盘图标点击测试
- [ ] 单击托盘图标显示/隐藏窗口
- [ ] 窗口显示时再次点击隐藏窗口
- [ ] 窗口正确定位和聚焦

### 3. 托盘右键菜单测试
- [ ] 右键点击托盘图标显示上下文菜单
- [ ] "显示窗口" 菜单项正常工作
- [ ] "新建会话" 菜单项正常工作
- [ ] "设置" 菜单项正常工作
- [ ] "关于" 菜单项显示正确信息
- [ ] "退出" 菜单项正常退出应用

### 4. 窗口管理测试
- [ ] 关闭窗口时隐藏到托盘而不是退出
- [ ] 最小化窗口时隐藏到托盘
- [ ] 窗口显示时正确聚焦到前台

### 5. 配置选项测试
- [ ] 设置中的"启动时显示窗口"选项正常工作
- [ ] 启用该选项后重启应用显示窗口
- [ ] 禁用该选项后重启应用隐藏到托盘

## 测试步骤

### 步骤1：验证启动行为
1. 关闭当前运行的应用实例
2. 重新启动应用
3. 确认：
   - 主窗口没有显示
   - 系统托盘（右下角）出现应用图标
   - 鼠标悬停在托盘图标上显示"桌面AI助手"

### 步骤2：测试托盘点击
1. 单击托盘图标
2. 确认主窗口显示并聚焦
3. 再次单击托盘图标
4. 确认主窗口隐藏

### 步骤3：测试托盘菜单
1. 右键点击托盘图标
2. 测试每个菜单项：
   - 点击"显示窗口"
   - 点击"新建会话"（应显示窗口并创建新会话）
   - 点击"设置"（应显示窗口并打开设置）
   - 点击"关于"（应显示关于对话框）

### 步骤4：测试窗口行为
1. 显示主窗口
2. 点击窗口的关闭按钮（X）
3. 确认窗口隐藏而不是退出应用
4. 显示主窗口
5. 点击最小化按钮
6. 确认窗口隐藏到托盘

### 步骤5：测试配置选项
1. 显示主窗口
2. 打开设置
3. 找到"启动时显示窗口"选项
4. 启用该选项并保存
5. 重启应用
6. 确认应用启动时显示主窗口

## 预期结果

✅ **成功实现的功能：**
- 应用启动时隐藏到系统托盘
- 托盘图标正确显示
- 托盘菜单结构完整
- 窗口关闭时隐藏而不退出
- 配置选项已添加

🔄 **需要验证的功能：**
- 托盘点击和菜单交互
- 窗口显示/隐藏逻辑
- 配置选项的实际效果

## 故障排除

如果遇到问题：
1. 检查控制台输出是否有错误
2. 确认托盘图标文件存在
3. 验证IPC通信是否正常
4. 检查配置文件是否正确保存
