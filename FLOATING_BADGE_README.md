# 桌面浮标功能说明

## 功能概述

桌面浮标是一个始终显示在桌面右侧的圆形图标，提供快速访问桌面AI助手的便捷方式。

## 功能特性

### 🎯 核心功能
- **桌面浮标显示**: 在桌面右侧显示一个60x60像素的圆形浮标
- **一键唤醒**: 点击浮标即可显示主应用窗口
- **始终置顶**: 浮标始终保持在所有窗口之上
- **智能定位**: 自动定位在屏幕右侧30%高度位置

### 🎨 视觉设计
- **渐变背景**: 蓝色渐变背景 (#1677ff → #4096ff → #69b1ff)
- **悬停效果**: 鼠标悬停时轻微放大和阴影增强
- **状态指示**: 右上角状态指示器显示应用状态
- **工具提示**: 悬停时显示"桌面AI助手"提示

### 🔄 状态管理
- **正常状态**: 蓝色渐变，绿色状态指示器
- **活跃状态**: 绿色渐变，表示应用正在使用
- **思考状态**: 橙色渐变，状态指示器闪烁动画

## 技术实现

### 文件结构
```
src/main/floatingBadge.ts    # 浮标管理器主文件
src/main/main.ts             # 主应用集成
src/shared/constants.ts      # IPC通信常量
src/main/preload.ts          # 预加载脚本API
```

### 核心类：FloatingBadgeManager

#### 主要方法
- `createFloatingBadge(onBadgeClick)`: 创建浮标窗口
- `showBadge()`: 显示浮标
- `hideBadge()`: 隐藏浮标
- `destroyBadge()`: 销毁浮标
- `setBadgeStatus(status)`: 设置浮标状态
- `setBadgeText(text)`: 设置浮标文字
- `setBadgeTooltip(tooltip)`: 设置提示文本

#### 窗口配置
```typescript
{
  width: 60,
  height: 60,
  frame: false,
  transparent: true,
  alwaysOnTop: true,
  skipTaskbar: true,
  focusable: false,
  resizable: false
}
```

### IPC通信

#### 新增通信频道
- `BADGE_CLICK`: 浮标点击事件
- `BADGE_SHOW`: 显示浮标
- `BADGE_HIDE`: 隐藏浮标

#### API接口
```typescript
// 渲染进程可用API
window.electronAPI.badge.onClick()
window.electronAPI.onBadgeClick()
```

## 使用方法

### 基本使用
1. 启动应用后，浮标会自动显示在桌面右侧
2. 点击浮标可以显示/隐藏主窗口
3. 浮标会根据应用状态改变颜色和动画

### 程序化控制
```typescript
// 显示浮标
this.floatingBadgeManager.showBadge();

// 隐藏浮标
this.floatingBadgeManager.hideBadge();

// 设置状态
this.floatingBadgeManager.setBadgeStatus('thinking');

// 设置文字
this.floatingBadgeManager.setBadgeText('AI');

// 设置提示
this.floatingBadgeManager.setBadgeTooltip('桌面AI助手');
```

## 兼容性

- **操作系统**: Windows 10/11
- **Electron版本**: 28.3.3+
- **屏幕分辨率**: 支持所有常见分辨率
- **多显示器**: 自动定位到主显示器

## 故障排除

### 浮标不显示
1. 检查应用是否正常启动
2. 查看控制台错误信息
3. 确认屏幕分辨率检测正常

### 点击无响应
1. 检查IPC通信是否正常
2. 确认主窗口管理器工作正常
3. 查看浮标窗口是否正确创建

### 位置异常
1. 检查屏幕工作区域计算
2. 确认多显示器配置
3. 验证窗口定位逻辑

## 开发说明

### 添加新功能
1. 在 `FloatingBadgeManager` 类中添加方法
2. 更新 IPC 通信频道
3. 在主应用中集成新功能
4. 更新预加载脚本API

### 自定义样式
修改 `floatingBadge.ts` 中的 `badgeHTML` 变量来自定义浮标外观。

### 调试模式
设置环境变量 `DEBUG_BADGE=true` 启用浮标调试模式。

## 更新日志

### v1.0.0 (2024-01-25)
- ✨ 初始版本发布
- 🎯 基础浮标显示功能
- 🖱️ 点击交互功能
- 🎨 状态指示和动画效果
- 📱 响应式定位系统
