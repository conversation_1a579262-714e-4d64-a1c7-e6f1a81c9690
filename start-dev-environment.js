// 完整开发环境启动脚本
const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动桌面AI助手开发环境...\n');

// 检查依赖
function checkDependencies() {
  console.log('📦 检查项目依赖...');
  
  if (!fs.existsSync('./node_modules')) {
    console.log('❌ node_modules不存在，请先运行: npm install');
    process.exit(1);
  }
  
  if (!fs.existsSync('./docs/env')) {
    console.log('❌ API密钥文件不存在，请确保 docs/env 文件存在');
    process.exit(1);
  }
  
  console.log('✅ 依赖检查通过\n');
}

// 编译主进程
function buildMain() {
  return new Promise((resolve, reject) => {
    console.log('🔨 编译Electron主进程...');
    
    const buildProcess = spawn('npx', ['webpack', '--config', 'webpack.main.config.js', '--mode', 'development'], {
      stdio: 'pipe',
      shell: true
    });
    
    buildProcess.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    buildProcess.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 主进程编译完成\n');
        resolve();
      } else {
        console.log('❌ 主进程编译失败');
        reject(new Error(`编译失败，退出码: ${code}`));
      }
    });
  });
}

// 启动前端开发服务器
function startRenderer() {
  console.log('🌐 启动前端开发服务器...');
  
  const rendererProcess = spawn('npx', ['webpack', 'serve', '--config', 'webpack.renderer.config.js'], {
    stdio: 'pipe',
    shell: true
  });
  
  rendererProcess.stdout.on('data', (data) => {
    const output = data.toString();
    if (output.includes('webpack compiled')) {
      console.log('✅ 前端开发服务器启动成功 (http://localhost:3000)\n');
    }
  });
  
  rendererProcess.stderr.on('data', (data) => {
    // 忽略webpack的警告信息
  });
  
  return rendererProcess;
}

// 启动Electron应用
function startElectron() {
  console.log('⚡ 启动Electron应用...');
  
  const electronProcess = spawn('npm', ['start'], {
    stdio: 'pipe',
    shell: true
  });
  
  electronProcess.stdout.on('data', (data) => {
    process.stdout.write(data);
  });
  
  electronProcess.stderr.on('data', (data) => {
    process.stderr.write(data);
  });
  
  return electronProcess;
}

// 主启动函数
async function startDevelopment() {
  try {
    checkDependencies();
    
    // 编译主进程
    await buildMain();
    
    // 启动前端开发服务器
    const rendererProcess = startRenderer();
    
    // 等待前端服务器启动
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 启动Electron应用
    const electronProcess = startElectron();
    
    console.log('🎉 开发环境启动完成！\n');
    console.log('📋 服务状态:');
    console.log('  • 前端开发服务器: http://localhost:3000');
    console.log('  • Electron应用: 已启动');
    console.log('  • API服务: 通义千问API已配置\n');
    
    console.log('💡 使用说明:');
    console.log('  • 在Electron应用中测试完整功能');
    console.log('  • 在浏览器中访问 http://localhost:3000 进行UI调试');
    console.log('  • 修改代码后前端会自动热重载');
    console.log('  • 修改主进程代码需要重启Electron\n');
    
    console.log('🛑 停止开发环境: 按 Ctrl+C\n');
    
    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 正在停止开发环境...');
      
      if (rendererProcess) {
        rendererProcess.kill();
      }
      
      if (electronProcess) {
        electronProcess.kill();
      }
      
      console.log('✅ 开发环境已停止');
      process.exit(0);
    });
    
    // 保持进程运行
    process.stdin.resume();
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  }
}

// 运行启动脚本
startDevelopment();
