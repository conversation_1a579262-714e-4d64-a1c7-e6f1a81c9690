@echo off
echo 🚀 快速启动桌面AI助手...
echo.

echo 📋 检查环境...
node --version
npm --version
echo.

echo 🔧 构建应用...
echo 构建主进程...
call npx webpack --config webpack.main.config.js
if %errorlevel% neq 0 (
    echo ❌ 主进程构建失败
    pause
    exit /b 1
)

echo 构建渲染进程...
call npx webpack --config webpack.renderer.config.js --mode production
if %errorlevel% neq 0 (
    echo ❌ 渲染进程构建失败
    pause
    exit /b 1
)

echo ✅ 构建完成
echo.

echo 🎯 启动应用...
call npm start

pause
