import React, { useState, useRef } from 'react';
import { Input, Button, Space, App } from 'antd';
import { SendOutlined, LoadingOutlined } from '@ant-design/icons';
import { useAppStore } from '../store/useAppStore';
import { useMessageService } from '../hooks/useMessageService';
import { useAIService } from '../hooks/useAIService';

const { TextArea } = Input;

interface MessageInputProps {
  sessionId: string;
}

const MessageInput: React.FC<MessageInputProps> = ({ sessionId }) => {
  const [inputValue, setInputValue] = useState('');
  const [isSending, setIsSending] = useState(false);
  const inputRef = useRef<any>(null);

  const { isLoading } = useAppStore();
  const { sendMessage } = useMessageService();
  const { sendToAI } = useAIService();
  const { message } = App.useApp();

  const handleSend = async () => {
    const content = inputValue.trim();
    if (!content || isSending) return;

    try {
      setIsSending(true);
      setInputValue('');

      // 发送用户消息
      const userMessage = await sendMessage(content, sessionId);

      // 发送到AI
      await sendToAI(content, sessionId);

      // 聚焦输入框
      inputRef.current?.focus();
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请重试');
      // 恢复输入内容
      setInputValue(content);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
  };

  const isDisabled = isSending || isLoading;

  return (
    <div style={{ padding: '16px 24px' }}>
      <Space.Compact style={{ width: '100%' }}>
        <TextArea
          ref={inputRef}
          value={inputValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder="输入消息... (Enter发送，Shift+Enter换行)"
          autoSize={{ minRows: 1, maxRows: 6 }}
          disabled={isDisabled}
          className="chat-input"
          style={{
            resize: 'none',
            borderRadius: '20px 0 0 20px'
          }}
          data-testid="chat-input"
        />
        <Button
          type="primary"
          icon={isSending ? <LoadingOutlined /> : <SendOutlined />}
          onClick={handleSend}
          disabled={isDisabled || !inputValue.trim()}
          style={{
            height: 'auto',
            minHeight: '40px',
            borderRadius: '0 20px 20px 0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          data-testid="send-btn"
        >
          发送
        </Button>
      </Space.Compact>
      
      {/* 输入提示 */}
      <div style={{ marginTop: '8px', fontSize: '12px', color: '#999' }}>
        按 Enter 发送消息，Shift + Enter 换行
      </div>
    </div>
  );
};

export default MessageInput;
