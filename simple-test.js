// 简单测试脚本，验证基本功能
const { app, BrowserWindow, Notification, globalShortcut } = require('electron');

console.log('🚀 启动简单测试...');

let mainWindow = null;

function createWindow() {
  console.log('📱 创建窗口...');
  
  mainWindow = new BrowserWindow({
    width: 400,
    height: 300,
    show: false, // 初始隐藏
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // 加载简单的HTML内容
  mainWindow.loadURL('data:text/html,<h1>桌面AI助手测试</h1><p>应用正在后台运行</p><p>按 Ctrl+Shift+A 显示/隐藏窗口</p>');
  
  console.log('✅ 窗口创建完成');
}

function showNotification() {
  console.log('🔔 尝试显示通知...');
  
  if (Notification.isSupported()) {
    console.log('✅ 系统支持通知');
    
    const notification = new Notification({
      title: '桌面AI助手测试',
      body: '应用已启动！按 Ctrl+Shift+A 显示窗口'
    });
    
    notification.on('click', () => {
      console.log('📱 通知被点击');
      if (mainWindow) {
        mainWindow.show();
        mainWindow.focus();
      }
    });
    
    notification.show();
    console.log('✅ 通知已显示');
  } else {
    console.log('❌ 系统不支持通知');
  }
}

function registerShortcuts() {
  console.log('⌨️ 注册快捷键...');
  
  const ret = globalShortcut.register('CommandOrControl+Shift+A', () => {
    console.log('⌨️ 快捷键被按下');
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
        console.log('🙈 窗口已隐藏');
      } else {
        mainWindow.show();
        mainWindow.focus();
        console.log('👀 窗口已显示');
      }
    }
  });

  if (ret) {
    console.log('✅ 快捷键注册成功');
  } else {
    console.log('❌ 快捷键注册失败');
  }
}

app.whenReady().then(() => {
  console.log('✅ Electron应用准备就绪');
  
  createWindow();
  showNotification();
  registerShortcuts();
  
  console.log('🎯 测试完成！应用应该：');
  console.log('1. 显示系统通知');
  console.log('2. 响应 Ctrl+Shift+A 快捷键');
  console.log('3. 窗口初始隐藏');
});

app.on('window-all-closed', () => {
  console.log('🔚 所有窗口关闭，但应用继续运行');
  // 不退出应用
});

app.on('before-quit', () => {
  console.log('🔚 应用即将退出，注销快捷键');
  globalShortcut.unregisterAll();
});

// 5分钟后自动退出
setTimeout(() => {
  console.log('⏰ 测试时间结束，退出应用');
  app.quit();
}, 5 * 60 * 1000);
